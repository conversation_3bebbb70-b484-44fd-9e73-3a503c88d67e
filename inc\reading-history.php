<?php
/**
 * Reading History Functions
 * 
 * @package EpicNovelTheme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Track reading progress when user visits a chapter
 */
function epic_track_reading_progress() {
    if (is_single() && is_user_logged_in()) {
        $post_id = get_the_ID();
        $user_id = get_current_user_id();
        
        // Update reading history with initial progress
        epic_update_reading_history($user_id, $post_id, 0, 0);
    }
}
add_action('wp_head', 'epic_track_reading_progress');

/**
 * AJAX handler for updating reading progress
 */
function epic_ajax_update_reading_progress() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $user_id = get_current_user_id();
    $post_id = intval($_POST['post_id']);
    $progress = floatval($_POST['progress']);
    $reading_time = intval($_POST['reading_time']);
    
    if (!$post_id) {
        wp_send_json_error('Invalid post ID');
        return;
    }
    
    $result = epic_update_reading_history($user_id, $post_id, $progress, $reading_time);
    
    if ($result) {
        wp_send_json_success(array(
            'message' => __('Reading progress updated', 'epic-novel-theme'),
            'progress' => $progress
        ));
    } else {
        wp_send_json_error('Failed to update reading progress');
    }
}
add_action('wp_ajax_epic_update_reading_progress', 'epic_ajax_update_reading_progress');

/**
 * AJAX handler for getting reading history
 */
function epic_ajax_get_reading_history() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $user_id = get_current_user_id();
    $page = intval($_POST['page'] ?? 1);
    $per_page = 20;
    
    $history = epic_get_user_reading_history($user_id, $per_page);
    
    $formatted_history = array();
    foreach ($history as $item) {
        $post = get_post($item->post_id);
        if ($post) {
            $formatted_history[] = array(
                'post_id' => $item->post_id,
                'post_title' => $post->post_title,
                'post_url' => get_permalink($post->ID),
                'novel_title' => $item->novel_title,
                'chapter_number' => $item->chapter_number,
                'reading_progress' => $item->reading_progress,
                'last_read' => $item->last_read,
                'reading_time' => $item->reading_time,
                'excerpt' => get_the_excerpt($post->ID)
            );
        }
    }
    
    wp_send_json_success(array(
        'history' => $formatted_history,
        'page' => $page,
        'has_more' => count($history) === $per_page
    ));
}
add_action('wp_ajax_epic_get_reading_history', 'epic_ajax_get_reading_history');

/**
 * Create reading history page
 */
function epic_create_reading_history_page() {
    // Check if reading history page exists
    $history_page = get_page_by_path('reading-history');
    
    if (!$history_page) {
        // Create reading history page
        $page_data = array(
            'post_title' => __('Reading History', 'epic-novel-theme'),
            'post_content' => '[epic_reading_history]',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'reading-history'
        );
        
        wp_insert_post($page_data);
    }
}
add_action('after_switch_theme', 'epic_create_reading_history_page');

/**
 * Reading history shortcode
 */
function epic_reading_history_shortcode($atts) {
    if (!is_user_logged_in()) {
        return '<p>' . __('Please log in to view your reading history.', 'epic-novel-theme') . '</p>';
    }
    
    $atts = shortcode_atts(array(
        'per_page' => 20
    ), $atts);
    
    ob_start();
    ?>
    
    <div class="reading-history-container">
        <div class="history-header">
            <h2><?php _e('Reading History', 'epic-novel-theme'); ?></h2>
            
            <div class="history-actions">
                <button class="refresh-history" id="refresh-history">
                    <i class="fas fa-sync-alt"></i>
                    <?php _e('Refresh', 'epic-novel-theme'); ?>
                </button>
                
                <button class="clear-history" id="clear-history">
                    <i class="fas fa-trash"></i>
                    <?php _e('Clear History', 'epic-novel-theme'); ?>
                </button>
            </div>
        </div>
        
        <div class="history-list" id="history-list">
            <div class="loading-history">
                <div class="spinner"></div>
                <p><?php _e('Loading reading history...', 'epic-novel-theme'); ?></p>
            </div>
        </div>
        
        <div class="history-pagination" id="history-pagination" style="display: none;">
            <button class="load-more-history" id="load-more-history">
                <?php _e('Load More', 'epic-novel-theme'); ?>
            </button>
        </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        let currentPage = 1;
        let hasMore = true;
        
        function loadHistory(page = 1, append = false) {
            $.ajax({
                url: epic_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'epic_get_reading_history',
                    nonce: epic_ajax.nonce,
                    page: page
                },
                success: function(response) {
                    if (response.success) {
                        const history = response.data.history;
                        hasMore = response.data.has_more;
                        
                        let html = '';
                        history.forEach(function(item) {
                            const progressWidth = Math.round(item.reading_progress);
                            const readingTime = Math.round(item.reading_time / 60); // Convert to minutes
                            const lastRead = new Date(item.last_read).toLocaleDateString();
                            
                            html += `
                                <div class="history-item">
                                    <div class="history-content">
                                        <h3><a href="${item.post_url}">${item.post_title}</a></h3>
                                        <p class="history-meta">
                                            ${item.novel_title ? `<span class="novel-title">${item.novel_title}</span>` : ''}
                                            ${item.chapter_number ? `<span class="chapter-number">Chapter ${item.chapter_number}</span>` : ''}
                                            <span class="last-read">Last read: ${lastRead}</span>
                                            ${readingTime > 0 ? `<span class="reading-time">${readingTime} min</span>` : ''}
                                        </p>
                                        ${item.excerpt ? `<p class="history-excerpt">${item.excerpt}</p>` : ''}
                                        
                                        <div class="reading-progress-bar">
                                            <div class="progress-fill" style="width: ${progressWidth}%"></div>
                                            <span class="progress-text">${progressWidth}% complete</span>
                                        </div>
                                    </div>
                                    <div class="history-actions">
                                        <a href="${item.post_url}" class="continue-reading-btn">
                                            <i class="fas fa-book-open"></i>
                                            ${item.reading_progress < 100 ? '<?php _e('Continue Reading', 'epic-novel-theme'); ?>' : '<?php _e('Read Again', 'epic-novel-theme'); ?>'}
                                        </a>
                                    </div>
                                </div>
                            `;
                        });
                        
                        if (append) {
                            $('#history-list').append(html);
                        } else {
                            $('#history-list').html(html);
                        }
                        
                        // Show/hide pagination
                        if (hasMore) {
                            $('#history-pagination').show();
                        } else {
                            $('#history-pagination').hide();
                        }
                        
                        if (history.length === 0 && page === 1) {
                            $('#history-list').html('<p class="no-history"><?php _e('No reading history found.', 'epic-novel-theme'); ?></p>');
                        }
                    } else {
                        $('#history-list').html('<p class="error">' + response.data + '</p>');
                    }
                },
                error: function() {
                    $('#history-list').html('<p class="error"><?php _e('Failed to load reading history.', 'epic-novel-theme'); ?></p>');
                }
            });
        }
        
        // Initial load
        loadHistory();
        
        // Load more
        $('#load-more-history').on('click', function() {
            currentPage++;
            loadHistory(currentPage, true);
        });
        
        // Refresh
        $('#refresh-history').on('click', function() {
            currentPage = 1;
            loadHistory(currentPage);
        });
        
        // Clear history
        $('#clear-history').on('click', function() {
            if (confirm('<?php _e('Are you sure you want to clear your reading history? This action cannot be undone.', 'epic-novel-theme'); ?>')) {
                $.ajax({
                    url: epic_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'epic_clear_reading_history',
                        nonce: epic_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#history-list').html('<p class="no-history"><?php _e('Reading history cleared.', 'epic-novel-theme'); ?></p>');
                            $('#history-pagination').hide();
                        } else {
                            alert('<?php _e('Failed to clear reading history.', 'epic-novel-theme'); ?>');
                        }
                    }
                });
            }
        });
    });
    </script>
    
    <?php
    return ob_get_clean();
}
add_shortcode('epic_reading_history', 'epic_reading_history_shortcode');

/**
 * AJAX handler for clearing reading history
 */
function epic_ajax_clear_reading_history() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    global $wpdb;
    $user_id = get_current_user_id();
    $history_table = $wpdb->prefix . 'epic_reading_history';
    
    $result = $wpdb->delete(
        $history_table,
        array('user_id' => $user_id),
        array('%d')
    );
    
    if ($result !== false) {
        wp_send_json_success(array(
            'message' => __('Reading history cleared', 'epic-novel-theme')
        ));
    } else {
        wp_send_json_error('Failed to clear reading history');
    }
}
add_action('wp_ajax_epic_clear_reading_history', 'epic_ajax_clear_reading_history');

/**
 * Add continue reading widget to dashboard
 */
function epic_continue_reading_widget() {
    if (!is_user_logged_in()) {
        return;
    }
    
    $user_id = get_current_user_id();
    $continue_reading = epic_get_continue_reading($user_id, 5);
    
    if (empty($continue_reading)) {
        return;
    }
    
    ?>
    <div class="continue-reading-widget">
        <h3><?php _e('Continue Reading', 'epic-novel-theme'); ?></h3>
        
        <div class="continue-reading-list">
            <?php foreach ($continue_reading as $item) : ?>
                <div class="continue-reading-item">
                    <div class="item-content">
                        <h4><a href="<?php echo get_permalink($item->post_id); ?>"><?php echo esc_html($item->post_title); ?></a></h4>
                        <?php if ($item->novel_title) : ?>
                            <p class="novel-title"><?php echo esc_html($item->novel_title); ?></p>
                        <?php endif; ?>
                        
                        <div class="reading-progress-bar">
                            <div class="progress-fill" style="width: <?php echo round($item->reading_progress); ?>%"></div>
                            <span class="progress-text"><?php echo round($item->reading_progress); ?>% complete</span>
                        </div>
                    </div>
                    
                    <a href="<?php echo get_permalink($item->post_id); ?>" class="continue-btn">
                        <i class="fas fa-play"></i>
                        <?php _e('Continue', 'epic-novel-theme'); ?>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="widget-footer">
            <a href="<?php echo home_url('/reading-history'); ?>" class="view-all-link">
                <?php _e('View All History', 'epic-novel-theme'); ?>
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>
    <?php
}

/**
 * Add reading progress tracking script to single posts
 */
function epic_add_reading_progress_script() {
    if (is_single() && is_user_logged_in()) {
        ?>
        <script>
        jQuery(document).ready(function($) {
            let startTime = Date.now();
            let lastProgress = 0;
            let progressUpdateInterval;
            
            function updateProgress() {
                const scrollTop = $(window).scrollTop();
                const docHeight = $(document).height() - $(window).height();
                const progress = Math.min(100, Math.max(0, (scrollTop / docHeight) * 100));
                
                // Only update if progress increased by at least 5%
                if (progress > lastProgress + 5) {
                    const readingTime = Math.round((Date.now() - startTime) / 1000);
                    
                    $.ajax({
                        url: epic_ajax.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'epic_update_reading_progress',
                            nonce: epic_ajax.nonce,
                            post_id: <?php echo get_the_ID(); ?>,
                            progress: progress,
                            reading_time: readingTime
                        }
                    });
                    
                    lastProgress = progress;
                }
            }
            
            // Update progress every 30 seconds
            progressUpdateInterval = setInterval(updateProgress, 30000);
            
            // Update progress on scroll
            $(window).on('scroll', function() {
                clearTimeout(window.scrollTimeout);
                window.scrollTimeout = setTimeout(updateProgress, 1000);
            });
            
            // Final update when leaving page
            $(window).on('beforeunload', function() {
                updateProgress();
            });
        });
        </script>
        <?php
    }
}
add_action('wp_footer', 'epic_add_reading_progress_script');
