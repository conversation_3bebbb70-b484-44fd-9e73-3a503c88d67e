<?php
/**
 * The template for displaying the footer
 * 
 * @package EpicNovelTheme
 */
?>

    <footer id="colophon" class="site-footer">
        <div class="container">
            
            <!-- Footer Widgets -->
            <?php if (is_active_sidebar('footer-1') || is_active_sidebar('footer-2') || is_active_sidebar('footer-3') || is_active_sidebar('footer-4')) : ?>
                <div class="footer-widgets">
                    <div class="footer-widget-area">
                        <?php if (is_active_sidebar('footer-1')) : ?>
                            <div class="footer-widget">
                                <?php dynamic_sidebar('footer-1'); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (is_active_sidebar('footer-2')) : ?>
                            <div class="footer-widget">
                                <?php dynamic_sidebar('footer-2'); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (is_active_sidebar('footer-3')) : ?>
                            <div class="footer-widget">
                                <?php dynamic_sidebar('footer-3'); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (is_active_sidebar('footer-4')) : ?>
                            <div class="footer-widget">
                                <?php dynamic_sidebar('footer-4'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Footer Navigation -->
            <?php if (has_nav_menu('footer')) : ?>
                <nav class="footer-navigation">
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'footer',
                        'menu_id'        => 'footer-menu',
                        'container'      => false,
                        'depth'          => 1,
                    ));
                    ?>
                </nav>
            <?php endif; ?>

            <!-- Site Info -->
            <div class="site-info">
                <div class="copyright">
                    <p>&copy; <?php echo date('Y'); ?> 
                        <a href="<?php echo esc_url(home_url('/')); ?>">
                            <?php bloginfo('name'); ?>
                        </a>. 
                        <?php _e('All rights reserved.', 'epic-novel-theme'); ?>
                    </p>
                </div>
                
                <div class="theme-credit">
                    <p>
                        <?php
                        printf(
                            __('Powered by %1$s | Theme: %2$s', 'epic-novel-theme'),
                            '<a href="https://wordpress.org/">WordPress</a>',
                            '<a href="#" rel="designer">Epic Novel Theme</a>'
                        );
                        ?>
                    </p>
                </div>
            </div>

            <!-- Back to Top Button -->
            <button class="back-to-top" id="back-to-top" aria-label="<?php _e('Back to top', 'epic-novel-theme'); ?>">
                <i class="fas fa-chevron-up"></i>
            </button>
        </div>
    </footer>

</div><!-- #page -->

<!-- Reading Controls (for single post pages) -->
<?php if (is_single()) : ?>
    <div class="reading-controls" id="reading-controls">
        <div class="font-size-control">
            <button class="font-size-btn" id="decrease-font" aria-label="<?php _e('Decrease font size', 'epic-novel-theme'); ?>">
                <i class="fas fa-minus"></i>
            </button>
            <span class="font-size-display" id="font-size-display">100%</span>
            <button class="font-size-btn" id="increase-font" aria-label="<?php _e('Increase font size', 'epic-novel-theme'); ?>">
                <i class="fas fa-plus"></i>
            </button>
        </div>
        
        <button class="reading-mode-btn" id="reading-mode-btn" aria-label="<?php _e('Toggle reading mode', 'epic-novel-theme'); ?>">
            <i class="fas fa-book-open"></i>
        </button>
        
        <?php if (is_user_logged_in()) : ?>
            <button class="bookmark-btn" data-post-id="<?php echo get_the_ID(); ?>" aria-label="<?php _e('Bookmark this chapter', 'epic-novel-theme'); ?>">
                <i class="fas fa-bookmark"></i>
            </button>
        <?php endif; ?>
    </div>
<?php endif; ?>

<!-- Mobile Reading Controls -->
<?php if (is_single() && wp_is_mobile()) : ?>
    <div class="mobile-reading-controls">
        <div class="mobile-controls-content">
            <div class="font-controls">
                <button class="font-size-btn" id="mobile-decrease-font">A-</button>
                <span class="font-size-display" id="mobile-font-size-display">100%</span>
                <button class="font-size-btn" id="mobile-increase-font">A+</button>
            </div>
            
            <button class="night-mode-toggle mobile-night-toggle">
                <i class="fas fa-moon"></i>
            </button>
            
            <?php if (is_user_logged_in()) : ?>
                <button class="bookmark-btn mobile-bookmark" data-post-id="<?php echo get_the_ID(); ?>">
                    <i class="fas fa-bookmark"></i>
                </button>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<!-- Cookie Consent Notice -->
<div class="cookie-notice" id="cookie-notice" style="display: none;">
    <div class="cookie-content">
        <p><?php _e('This website uses cookies to enhance your reading experience and provide personalized content.', 'epic-novel-theme'); ?></p>
        <div class="cookie-actions">
            <button class="accept-cookies" id="accept-cookies">
                <?php _e('Accept', 'epic-novel-theme'); ?>
            </button>
            <button class="decline-cookies" id="decline-cookies">
                <?php _e('Decline', 'epic-novel-theme'); ?>
            </button>
            <a href="<?php echo esc_url(get_privacy_policy_url()); ?>" class="privacy-policy-link">
                <?php _e('Privacy Policy', 'epic-novel-theme'); ?>
            </a>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner"></div>
        <p><?php _e('Loading...', 'epic-novel-theme'); ?></p>
    </div>
</div>

<!-- Google OAuth Script -->
<?php if (!is_user_logged_in()) : ?>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <div id="g_id_onload"
         data-client_id="<?php echo esc_attr(get_option('epic_google_client_id')); ?>"
         data-callback="handleCredentialResponse"
         data-auto_prompt="false">
    </div>
<?php endif; ?>

<?php wp_footer(); ?>

<script>
// Initialize theme JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Night mode functionality
    const nightModeToggle = document.getElementById('night-mode-toggle');
    const body = document.body;
    
    // Check for saved night mode preference
    const savedNightMode = localStorage.getItem('epic-night-mode');
    if (savedNightMode === 'enabled') {
        body.classList.add('night-mode');
    }
    
    if (nightModeToggle) {
        nightModeToggle.addEventListener('click', function() {
            body.classList.toggle('night-mode');
            
            // Save preference
            if (body.classList.contains('night-mode')) {
                localStorage.setItem('epic-night-mode', 'enabled');
            } else {
                localStorage.setItem('epic-night-mode', 'disabled');
            }
        });
    }
    
    // Reading progress bar
    const progressBar = document.getElementById('reading-progress');
    if (progressBar) {
        window.addEventListener('scroll', function() {
            const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
            const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrolled = (winScroll / height) * 100;
            progressBar.style.width = scrolled + '%';
        });
    }
    
    // Back to top button
    const backToTop = document.getElementById('back-to-top');
    if (backToTop) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'block';
            } else {
                backToTop.style.display = 'none';
            }
        });
        
        backToTop.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    // Cookie notice
    const cookieNotice = document.getElementById('cookie-notice');
    const acceptCookies = document.getElementById('accept-cookies');
    const declineCookies = document.getElementById('decline-cookies');
    
    if (cookieNotice && !localStorage.getItem('epic-cookies-accepted')) {
        cookieNotice.style.display = 'block';
    }
    
    if (acceptCookies) {
        acceptCookies.addEventListener('click', function() {
            localStorage.setItem('epic-cookies-accepted', 'true');
            cookieNotice.style.display = 'none';
        });
    }
    
    if (declineCookies) {
        declineCookies.addEventListener('click', function() {
            localStorage.setItem('epic-cookies-accepted', 'false');
            cookieNotice.style.display = 'none';
        });
    }
});

// Google OAuth callback function
function handleCredentialResponse(response) {
    // Send the credential to your server
    fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=epic_google_login&credential=' + response.credential + '&nonce=<?php echo wp_create_nonce('epic_google_login'); ?>'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert('Login failed. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Login failed. Please try again.');
    });
}
</script>

</body>
</html>
