<?php
/**
 * SEO Functions and Schema Markup
 * 
 * @package EpicNovelTheme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Render SEO meta tags
 */
function epic_render_seo_meta() {
    global $post;
    
    // Default meta values
    $site_name = get_bloginfo('name');
    $site_description = get_bloginfo('description');
    $site_url = home_url();
    
    // Page-specific meta values
    $title = '';
    $description = '';
    $image = '';
    $url = '';
    $type = 'website';
    
    if (is_front_page()) {
        $title = $site_name . ' - ' . $site_description;
        $description = $site_description;
        $url = $site_url;
        $image = get_theme_mod('custom_logo') ? wp_get_attachment_image_url(get_theme_mod('custom_logo'), 'large') : '';
    } elseif (is_single()) {
        $title = get_the_title() . ' - ' . $site_name;
        $description = get_the_excerpt() ?: wp_trim_words(get_the_content(), 30);
        $url = get_permalink();
        $image = get_the_post_thumbnail_url($post->ID, 'large');
        $type = get_post_type() === 'novel' ? 'book' : 'article';
        
        // Add novel-specific meta for chapters
        if (get_post_type() === 'post') {
            $novel_title = get_post_meta($post->ID, 'novel_title', true);
            $chapter_number = get_post_meta($post->ID, 'chapter_number', true);
            
            if ($novel_title && $chapter_number) {
                $title = $novel_title . ' - Chapter ' . $chapter_number . ': ' . get_the_title() . ' - ' . $site_name;
            }
        }
    } elseif (is_post_type_archive('novel')) {
        $title = __('Novels', 'epic-novel-theme') . ' - ' . $site_name;
        $description = __('Browse our collection of light novels and web novels.', 'epic-novel-theme');
        $url = get_post_type_archive_link('novel');
    } elseif (is_tax()) {
        $term = get_queried_object();
        $title = $term->name . ' - ' . $site_name;
        $description = $term->description ?: sprintf(__('Browse novels in the %s category.', 'epic-novel-theme'), $term->name);
        $url = get_term_link($term);
    } elseif (is_archive()) {
        $title = get_the_archive_title() . ' - ' . $site_name;
        $description = get_the_archive_description();
        $url = get_pagenum_link();
    } elseif (is_search()) {
        $title = sprintf(__('Search Results for "%s"', 'epic-novel-theme'), get_search_query()) . ' - ' . $site_name;
        $description = sprintf(__('Search results for "%s" on %s.', 'epic-novel-theme'), get_search_query(), $site_name);
        $url = get_search_link();
    } elseif (is_404()) {
        $title = __('Page Not Found', 'epic-novel-theme') . ' - ' . $site_name;
        $description = __('The page you are looking for could not be found.', 'epic-novel-theme');
        $url = home_url($_SERVER['REQUEST_URI']);
    } else {
        $title = get_the_title() . ' - ' . $site_name;
        $description = get_the_excerpt() ?: $site_description;
        $url = get_permalink();
        $image = get_the_post_thumbnail_url($post->ID ?? 0, 'large');
    }
    
    // Clean up description
    $description = wp_strip_all_tags($description);
    $description = wp_trim_words($description, 30);
    
    // Default image if none found
    if (!$image) {
        $image = get_theme_mod('custom_logo') ? wp_get_attachment_image_url(get_theme_mod('custom_logo'), 'large') : '';
    }
    
    ?>
    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo esc_attr($description); ?>">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <link rel="canonical" href="<?php echo esc_url($url); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:locale" content="<?php echo esc_attr(get_locale()); ?>">
    <meta property="og:type" content="<?php echo esc_attr($type); ?>">
    <meta property="og:title" content="<?php echo esc_attr($title); ?>">
    <meta property="og:description" content="<?php echo esc_attr($description); ?>">
    <meta property="og:url" content="<?php echo esc_url($url); ?>">
    <meta property="og:site_name" content="<?php echo esc_attr($site_name); ?>">
    <?php if ($image) : ?>
    <meta property="og:image" content="<?php echo esc_url($image); ?>">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <?php endif; ?>
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo esc_attr($title); ?>">
    <meta name="twitter:description" content="<?php echo esc_attr($description); ?>">
    <?php if ($image) : ?>
    <meta name="twitter:image" content="<?php echo esc_url($image); ?>">
    <?php endif; ?>
    
    <!-- Additional Meta Tags for Novels -->
    <?php if (is_single() && get_post_type() === 'novel') : ?>
        <?php
        $writers = get_the_terms($post->ID, 'writer');
        $genres = get_the_terms($post->ID, 'genre');
        $rating = get_post_meta($post->ID, 'rating', true);
        $release_year = get_post_meta($post->ID, 'release_year', true);
        ?>
        
        <?php if ($writers) : ?>
        <meta name="book:author" content="<?php echo esc_attr(implode(', ', wp_list_pluck($writers, 'name'))); ?>">
        <?php endif; ?>
        
        <?php if ($genres) : ?>
        <meta name="book:genre" content="<?php echo esc_attr(implode(', ', wp_list_pluck($genres, 'name'))); ?>">
        <?php endif; ?>
        
        <?php if ($release_year) : ?>
        <meta name="book:release_date" content="<?php echo esc_attr($release_year); ?>">
        <?php endif; ?>
        
        <?php if ($rating) : ?>
        <meta name="book:rating" content="<?php echo esc_attr($rating); ?>">
        <?php endif; ?>
    <?php endif; ?>
    
    <?php
}

/**
 * Render Schema.org structured data
 */
function epic_render_schema_markup() {
    global $post;
    
    $schema = array();
    
    if (is_front_page()) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => get_bloginfo('name'),
            'description' => get_bloginfo('description'),
            'url' => home_url(),
            'potentialAction' => array(
                '@type' => 'SearchAction',
                'target' => array(
                    '@type' => 'EntryPoint',
                    'urlTemplate' => home_url('/?s={search_term_string}')
                ),
                'query-input' => 'required name=search_term_string'
            )
        );
    } elseif (is_single() && get_post_type() === 'novel') {
        $writers = get_the_terms($post->ID, 'writer');
        $genres = get_the_terms($post->ID, 'genre');
        $rating = get_post_meta($post->ID, 'rating', true);
        $rating_count = get_post_meta($post->ID, 'rating_count', true);
        $release_year = get_post_meta($post->ID, 'release_year', true);
        $novel_status = get_post_meta($post->ID, 'novel_status', true);
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Book',
            'name' => get_the_title(),
            'description' => get_the_excerpt() ?: wp_trim_words(get_the_content(), 30),
            'url' => get_permalink(),
            'image' => get_the_post_thumbnail_url($post->ID, 'large'),
            'datePublished' => get_the_date('c'),
            'dateModified' => get_the_modified_date('c'),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'url' => home_url()
            )
        );
        
        if ($writers) {
            $schema['author'] = array();
            foreach ($writers as $writer) {
                $schema['author'][] = array(
                    '@type' => 'Person',
                    'name' => $writer->name
                );
            }
        }
        
        if ($genres) {
            $schema['genre'] = wp_list_pluck($genres, 'name');
        }
        
        if ($rating && $rating_count) {
            $schema['aggregateRating'] = array(
                '@type' => 'AggregateRating',
                'ratingValue' => $rating,
                'ratingCount' => $rating_count,
                'bestRating' => 10,
                'worstRating' => 1
            );
        }
        
        if ($release_year) {
            $schema['datePublished'] = $release_year . '-01-01';
        }
        
        // Add work status
        if ($novel_status) {
            $schema['workExample'] = array(
                '@type' => 'Book',
                'bookFormat' => 'EBook',
                'inLanguage' => get_locale(),
                'workTranslation' => array(
                    '@type' => 'Book',
                    'inLanguage' => 'en'
                )
            );
        }
        
    } elseif (is_single() && get_post_type() === 'post') {
        $novel_title = get_post_meta($post->ID, 'novel_title', true);
        $chapter_number = get_post_meta($post->ID, 'chapter_number', true);
        $reading_time = epic_get_reading_time($post->ID);
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => get_the_title(),
            'description' => get_the_excerpt() ?: wp_trim_words(get_the_content(), 30),
            'url' => get_permalink(),
            'datePublished' => get_the_date('c'),
            'dateModified' => get_the_modified_date('c'),
            'author' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name')
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'url' => home_url()
            ),
            'mainEntityOfPage' => array(
                '@type' => 'WebPage',
                '@id' => get_permalink()
            )
        );
        
        if ($novel_title) {
            $schema['isPartOf'] = array(
                '@type' => 'Book',
                'name' => $novel_title
            );
        }
        
        if ($chapter_number) {
            $schema['position'] = $chapter_number;
        }
        
        if ($reading_time) {
            $schema['timeRequired'] = 'PT' . $reading_time . 'M';
        }
        
        // Add breadcrumb
        $schema['breadcrumb'] = array(
            '@type' => 'BreadcrumbList',
            'itemListElement' => array(
                array(
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => 'Home',
                    'item' => home_url()
                ),
                array(
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => 'Chapters',
                    'item' => home_url('/blog')
                ),
                array(
                    '@type' => 'ListItem',
                    'position' => 3,
                    'name' => get_the_title(),
                    'item' => get_permalink()
                )
            )
        );
        
    } elseif (is_post_type_archive('novel')) {
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'CollectionPage',
            'name' => __('Novels', 'epic-novel-theme'),
            'description' => __('Browse our collection of light novels and web novels.', 'epic-novel-theme'),
            'url' => get_post_type_archive_link('novel'),
            'mainEntity' => array(
                '@type' => 'ItemList',
                'name' => __('Novel Collection', 'epic-novel-theme'),
                'numberOfItems' => wp_count_posts('novel')->publish
            )
        );
    }
    
    if (!empty($schema)) {
        echo '<script type="application/ld+json">' . wp_json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
    }
}

/**
 * Add custom meta box for SEO settings
 */
function epic_add_seo_meta_box() {
    $post_types = array('post', 'page', 'novel');
    
    foreach ($post_types as $post_type) {
        add_meta_box(
            'epic_seo_settings',
            __('SEO Settings', 'epic-novel-theme'),
            'epic_seo_meta_box_callback',
            $post_type,
            'normal',
            'low'
        );
    }
}
add_action('add_meta_boxes', 'epic_add_seo_meta_box');

/**
 * SEO meta box callback
 */
function epic_seo_meta_box_callback($post) {
    wp_nonce_field('epic_seo_meta_nonce', 'epic_seo_meta_nonce');
    
    $seo_title = get_post_meta($post->ID, '_epic_seo_title', true);
    $seo_description = get_post_meta($post->ID, '_epic_seo_description', true);
    $seo_keywords = get_post_meta($post->ID, '_epic_seo_keywords', true);
    $seo_noindex = get_post_meta($post->ID, '_epic_seo_noindex', true);
    ?>
    
    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="epic_seo_title"><?php _e('SEO Title', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <input type="text" id="epic_seo_title" name="epic_seo_title" value="<?php echo esc_attr($seo_title); ?>" class="large-text" maxlength="60" />
                <p class="description"><?php _e('Custom title for search engines (max 60 characters).', 'epic-novel-theme'); ?></p>
                <div class="character-count">
                    <span id="title-count">0</span>/60 <?php _e('characters', 'epic-novel-theme'); ?>
                </div>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="epic_seo_description"><?php _e('Meta Description', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <textarea id="epic_seo_description" name="epic_seo_description" rows="3" class="large-text" maxlength="160"><?php echo esc_textarea($seo_description); ?></textarea>
                <p class="description"><?php _e('Custom description for search engines (max 160 characters).', 'epic-novel-theme'); ?></p>
                <div class="character-count">
                    <span id="description-count">0</span>/160 <?php _e('characters', 'epic-novel-theme'); ?>
                </div>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="epic_seo_keywords"><?php _e('Focus Keywords', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <input type="text" id="epic_seo_keywords" name="epic_seo_keywords" value="<?php echo esc_attr($seo_keywords); ?>" class="large-text" />
                <p class="description"><?php _e('Comma-separated keywords for this content.', 'epic-novel-theme'); ?></p>
            </td>
        </tr>
        
        <tr>
            <th scope="row"><?php _e('Search Engine Visibility', 'epic-novel-theme'); ?></th>
            <td>
                <label for="epic_seo_noindex">
                    <input type="checkbox" id="epic_seo_noindex" name="epic_seo_noindex" value="1" <?php checked($seo_noindex, 1); ?> />
                    <?php _e('Discourage search engines from indexing this page', 'epic-novel-theme'); ?>
                </label>
            </td>
        </tr>
    </table>
    
    <script>
    jQuery(document).ready(function($) {
        function updateCharacterCount(input, counter) {
            const count = input.val().length;
            counter.text(count);
            
            if (count > parseInt(counter.parent().text().match(/\/(\d+)/)[1])) {
                counter.css('color', 'red');
            } else {
                counter.css('color', 'inherit');
            }
        }
        
        const titleInput = $('#epic_seo_title');
        const titleCounter = $('#title-count');
        const descInput = $('#epic_seo_description');
        const descCounter = $('#description-count');
        
        // Initial count
        updateCharacterCount(titleInput, titleCounter);
        updateCharacterCount(descInput, descCounter);
        
        // Update on input
        titleInput.on('input', function() {
            updateCharacterCount($(this), titleCounter);
        });
        
        descInput.on('input', function() {
            updateCharacterCount($(this), descCounter);
        });
    });
    </script>
    
    <?php
}

/**
 * Save SEO meta data
 */
function epic_save_seo_meta($post_id) {
    // Check if nonce is valid
    if (!isset($_POST['epic_seo_meta_nonce']) || !wp_verify_nonce($_POST['epic_seo_meta_nonce'], 'epic_seo_meta_nonce')) {
        return;
    }
    
    // Check if user has permission to edit the post
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    // Check if not an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    // Save SEO fields
    $fields = array(
        'epic_seo_title' => '_epic_seo_title',
        'epic_seo_description' => '_epic_seo_description',
        'epic_seo_keywords' => '_epic_seo_keywords',
        'epic_seo_noindex' => '_epic_seo_noindex'
    );
    
    foreach ($fields as $field => $meta_key) {
        if (isset($_POST[$field])) {
            if ($field === 'epic_seo_description') {
                update_post_meta($post_id, $meta_key, sanitize_textarea_field($_POST[$field]));
            } elseif ($field === 'epic_seo_noindex') {
                update_post_meta($post_id, $meta_key, isset($_POST[$field]) ? 1 : 0);
            } else {
                update_post_meta($post_id, $meta_key, sanitize_text_field($_POST[$field]));
            }
        }
    }
}
add_action('save_post', 'epic_save_seo_meta');

/**
 * Add robots meta tag based on post settings
 */
function epic_add_robots_meta() {
    if (is_singular()) {
        $post_id = get_the_ID();
        $noindex = get_post_meta($post_id, '_epic_seo_noindex', true);
        
        if ($noindex) {
            echo '<meta name="robots" content="noindex, nofollow">' . "\n";
        }
    }
}
add_action('wp_head', 'epic_add_robots_meta', 1);

/**
 * Generate XML sitemap for novels
 */
function epic_generate_novel_sitemap() {
    if (isset($_GET['epic_sitemap']) && $_GET['epic_sitemap'] === 'novels') {
        header('Content-Type: application/xml; charset=utf-8');
        
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        // Add novel archive page
        echo '<url>' . "\n";
        echo '<loc>' . esc_url(get_post_type_archive_link('novel')) . '</loc>' . "\n";
        echo '<lastmod>' . date('c') . '</lastmod>' . "\n";
        echo '<changefreq>daily</changefreq>' . "\n";
        echo '<priority>0.8</priority>' . "\n";
        echo '</url>' . "\n";
        
        // Add individual novels
        $novels = get_posts(array(
            'post_type' => 'novel',
            'posts_per_page' => -1,
            'post_status' => 'publish'
        ));
        
        foreach ($novels as $novel) {
            echo '<url>' . "\n";
            echo '<loc>' . esc_url(get_permalink($novel->ID)) . '</loc>' . "\n";
            echo '<lastmod>' . date('c', strtotime($novel->post_modified)) . '</lastmod>' . "\n";
            echo '<changefreq>weekly</changefreq>' . "\n";
            echo '<priority>0.7</priority>' . "\n";
            echo '</url>' . "\n";
        }
        
        echo '</urlset>';
        exit;
    }
}
add_action('init', 'epic_generate_novel_sitemap');
