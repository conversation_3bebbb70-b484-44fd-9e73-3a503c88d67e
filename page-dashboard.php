<?php
/**
 * Template for user dashboard page
 * 
 * @package EpicNovelTheme
 */

// Redirect if not logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

get_header(); ?>

<div class="container">
    <main id="main" class="site-main">
        
        <header class="page-header">
            <h1 class="page-title"><?php _e('Dashboard', 'epic-novel-theme'); ?></h1>
            <p class="page-description"><?php _e('Welcome to your reading dashboard.', 'epic-novel-theme'); ?></p>
        </header>

        <div class="user-dashboard" id="user-dashboard">
            
            <div class="dashboard-sidebar">
                
                <!-- User Profile -->
                <div class="user-profile-widget">
                    <?php $current_user = wp_get_current_user(); ?>
                    <div class="user-avatar">
                        <?php echo get_avatar($current_user->ID, 80); ?>
                    </div>
                    <div class="user-info">
                        <h3><?php echo esc_html($current_user->display_name); ?></h3>
                        <p class="user-email"><?php echo esc_html($current_user->user_email); ?></p>
                        <p class="member-since">
                            <?php printf(__('Member since %s', 'epic-novel-theme'), date_i18n(get_option('date_format'), strtotime($current_user->user_registered))); ?>
                        </p>
                    </div>
                    <div class="profile-actions">
                        <a href="<?php echo get_edit_user_link(); ?>" class="edit-profile-btn">
                            <i class="fas fa-edit"></i>
                            <?php _e('Edit Profile', 'epic-novel-theme'); ?>
                        </a>
                    </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="quick-stats-widget">
                    <h3><?php _e('Your Statistics', 'epic-novel-theme'); ?></h3>
                    <div class="stats-grid" id="user-stats">
                        <div class="loading-stats">
                            <div class="spinner"></div>
                            <p><?php _e('Loading statistics...', 'epic-novel-theme'); ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="quick-actions-widget">
                    <h3><?php _e('Quick Actions', 'epic-novel-theme'); ?></h3>
                    <div class="actions-list">
                        <a href="<?php echo home_url('/bookmarks'); ?>" class="action-btn">
                            <i class="fas fa-bookmark"></i>
                            <?php _e('View Bookmarks', 'epic-novel-theme'); ?>
                        </a>
                        <a href="<?php echo home_url('/reading-history'); ?>" class="action-btn">
                            <i class="fas fa-history"></i>
                            <?php _e('Reading History', 'epic-novel-theme'); ?>
                        </a>
                        <a href="<?php echo get_post_type_archive_link('novel'); ?>" class="action-btn">
                            <i class="fas fa-book"></i>
                            <?php _e('Browse Novels', 'epic-novel-theme'); ?>
                        </a>
                        <a href="<?php echo home_url(); ?>" class="action-btn">
                            <i class="fas fa-home"></i>
                            <?php _e('Latest Chapters', 'epic-novel-theme'); ?>
                        </a>
                    </div>
                </div>
                
            </div>
            
            <div class="dashboard-content">
                
                <!-- Continue Reading -->
                <div class="dashboard-widget continue-reading-widget">
                    <div class="widget-header">
                        <h2><?php _e('Continue Reading', 'epic-novel-theme'); ?></h2>
                        <a href="<?php echo home_url('/reading-history'); ?>" class="view-all-link">
                            <?php _e('View All', 'epic-novel-theme'); ?>
                        </a>
                    </div>
                    <div class="widget-content" id="continue-reading-list">
                        <div class="loading-content">
                            <div class="spinner"></div>
                            <p><?php _e('Loading continue reading...', 'epic-novel-theme'); ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Bookmarks -->
                <div class="dashboard-widget recent-bookmarks-widget">
                    <div class="widget-header">
                        <h2><?php _e('Recent Bookmarks', 'epic-novel-theme'); ?></h2>
                        <a href="<?php echo home_url('/bookmarks'); ?>" class="view-all-link">
                            <?php _e('View All', 'epic-novel-theme'); ?>
                        </a>
                    </div>
                    <div class="widget-content" id="recent-bookmarks-list">
                        <div class="loading-content">
                            <div class="spinner"></div>
                            <p><?php _e('Loading bookmarks...', 'epic-novel-theme'); ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- Reading Preferences -->
                <div class="dashboard-widget preferences-widget">
                    <div class="widget-header">
                        <h2><?php _e('Reading Preferences', 'epic-novel-theme'); ?></h2>
                    </div>
                    <div class="widget-content">
                        <form id="preferences-form" class="preferences-form">
                            <div class="preference-group">
                                <label for="font-size-pref"><?php _e('Default Font Size', 'epic-novel-theme'); ?></label>
                                <select id="font-size-pref" name="font_size">
                                    <option value="80">80%</option>
                                    <option value="90">90%</option>
                                    <option value="100" selected>100%</option>
                                    <option value="110">110%</option>
                                    <option value="120">120%</option>
                                    <option value="130">130%</option>
                                    <option value="140">140%</option>
                                    <option value="150">150%</option>
                                </select>
                            </div>
                            
                            <div class="preference-group">
                                <label for="night-mode-pref"><?php _e('Default Theme', 'epic-novel-theme'); ?></label>
                                <select id="night-mode-pref" name="night_mode">
                                    <option value="light"><?php _e('Light Mode', 'epic-novel-theme'); ?></option>
                                    <option value="dark"><?php _e('Dark Mode', 'epic-novel-theme'); ?></option>
                                    <option value="auto"><?php _e('Auto (System)', 'epic-novel-theme'); ?></option>
                                </select>
                            </div>
                            
                            <div class="preference-group">
                                <label>
                                    <input type="checkbox" id="auto-bookmark-pref" name="auto_bookmark" value="1">
                                    <?php _e('Auto-bookmark chapters when reading', 'epic-novel-theme'); ?>
                                </label>
                            </div>
                            
                            <div class="preference-group">
                                <label>
                                    <input type="checkbox" id="email-notifications-pref" name="email_notifications" value="1">
                                    <?php _e('Email notifications for new chapters', 'epic-novel-theme'); ?>
                                </label>
                            </div>
                            
                            <div class="preference-group">
                                <label>
                                    <input type="checkbox" id="chapter-notifications-pref" name="chapter_notifications" value="1">
                                    <?php _e('Browser notifications for bookmarked novels', 'epic-novel-theme'); ?>
                                </label>
                            </div>
                            
                            <div class="preference-actions">
                                <button type="submit" class="save-preferences-btn">
                                    <i class="fas fa-save"></i>
                                    <?php _e('Save Preferences', 'epic-novel-theme'); ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="dashboard-widget activity-widget">
                    <div class="widget-header">
                        <h2><?php _e('Recent Activity', 'epic-novel-theme'); ?></h2>
                    </div>
                    <div class="widget-content">
                        <div class="activity-feed" id="activity-feed">
                            <div class="loading-content">
                                <div class="spinner"></div>
                                <p><?php _e('Loading activity...', 'epic-novel-theme'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
            
        </div>

    </main>
</div>

<script>
jQuery(document).ready(function($) {
    
    // Load dashboard data
    function loadDashboardData() {
        $.ajax({
            url: epic_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'epic_get_dashboard_data',
                nonce: epic_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    
                    // Update statistics
                    updateStatistics(data.statistics);
                    
                    // Update continue reading
                    updateContinueReading(data.continue_reading);
                    
                    // Update recent bookmarks
                    updateRecentBookmarks(data.recent_bookmarks);
                    
                } else {
                    console.error('Failed to load dashboard data:', response.data);
                }
            },
            error: function() {
                console.error('Failed to load dashboard data');
            }
        });
    }
    
    // Update statistics
    function updateStatistics(stats) {
        let html = `
            <div class="stat-item">
                <div class="stat-number">${stats.total_chapters_read}</div>
                <div class="stat-label"><?php _e('Chapters Read', 'epic-novel-theme'); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${stats.total_bookmarks}</div>
                <div class="stat-label"><?php _e('Bookmarks', 'epic-novel-theme'); ?></div>
            </div>
            <div class="stat-item">
                <div class="stat-number">${stats.reading_time_hours}h</div>
                <div class="stat-label"><?php _e('Reading Time', 'epic-novel-theme'); ?></div>
            </div>
        `;
        
        $('#user-stats').html(html);
    }
    
    // Update continue reading
    function updateContinueReading(continueReading) {
        let html = '';
        
        if (continueReading.length > 0) {
            continueReading.forEach(function(item) {
                const progressWidth = Math.round(item.reading_progress);
                const lastRead = new Date(item.last_read).toLocaleDateString();
                
                html += `
                    <div class="continue-reading-item">
                        <div class="item-content">
                            <h4><a href="${item.post_url}">${item.post_title}</a></h4>
                            ${item.novel_title ? `<p class="novel-title">${item.novel_title}</p>` : ''}
                            <div class="reading-progress-bar">
                                <div class="progress-fill" style="width: ${progressWidth}%"></div>
                                <span class="progress-text">${progressWidth}% complete</span>
                            </div>
                            <p class="last-read">Last read: ${lastRead}</p>
                        </div>
                        <div class="item-actions">
                            <a href="${item.post_url}" class="continue-btn">
                                <i class="fas fa-play"></i>
                                <?php _e('Continue', 'epic-novel-theme'); ?>
                            </a>
                        </div>
                    </div>
                `;
            });
        } else {
            html = '<p class="no-content"><?php _e('No reading progress found. Start reading some chapters!', 'epic-novel-theme'); ?></p>';
        }
        
        $('#continue-reading-list').html(html);
    }
    
    // Update recent bookmarks
    function updateRecentBookmarks(bookmarks) {
        let html = '';
        
        if (bookmarks.length > 0) {
            bookmarks.forEach(function(bookmark) {
                const bookmarkDate = new Date(bookmark.bookmark_date).toLocaleDateString();
                
                html += `
                    <div class="bookmark-item">
                        <div class="bookmark-content">
                            <h4><a href="${bookmark.post_url}">${bookmark.post_title}</a></h4>
                            <p class="bookmark-meta">
                                <span class="bookmark-type">${bookmark.post_type}</span>
                                <span class="bookmark-date">${bookmarkDate}</span>
                            </p>
                        </div>
                        <div class="bookmark-actions">
                            <a href="${bookmark.post_url}" class="read-btn">
                                <i class="fas fa-book-open"></i>
                                <?php _e('Read', 'epic-novel-theme'); ?>
                            </a>
                        </div>
                    </div>
                `;
            });
        } else {
            html = '<p class="no-content"><?php _e('No bookmarks found. Start bookmarking your favorite content!', 'epic-novel-theme'); ?></p>';
        }
        
        $('#recent-bookmarks-list').html(html);
    }
    
    // Load user preferences
    function loadUserPreferences() {
        // Load from localStorage for now
        const fontSize = localStorage.getItem('epic-font-size') || '100';
        const nightMode = localStorage.getItem('epic-night-mode') || 'light';
        
        $('#font-size-pref').val(fontSize);
        $('#night-mode-pref').val(nightMode === 'enabled' ? 'dark' : 'light');
    }
    
    // Save preferences
    $('#preferences-form').on('submit', function(e) {
        e.preventDefault();
        
        const formData = $(this).serializeArray();
        const preferences = {};
        
        formData.forEach(function(item) {
            preferences[item.name] = item.value;
        });
        
        // Handle checkboxes
        preferences.auto_bookmark = $('#auto-bookmark-pref').is(':checked') ? '1' : '0';
        preferences.email_notifications = $('#email-notifications-pref').is(':checked') ? '1' : '0';
        preferences.chapter_notifications = $('#chapter-notifications-pref').is(':checked') ? '1' : '0';
        
        $.ajax({
            url: epic_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'epic_update_user_preferences',
                nonce: epic_ajax.nonce,
                preferences: preferences
            },
            success: function(response) {
                if (response.success) {
                    // Update localStorage
                    localStorage.setItem('epic-font-size', preferences.font_size);
                    localStorage.setItem('epic-night-mode', preferences.night_mode === 'dark' ? 'enabled' : 'disabled');
                    
                    EpicTheme.showNotification('<?php _e('Preferences saved successfully!', 'epic-novel-theme'); ?>', 'success');
                } else {
                    EpicTheme.showNotification(response.data, 'error');
                }
            },
            error: function() {
                EpicTheme.showNotification('<?php _e('Failed to save preferences.', 'epic-novel-theme'); ?>', 'error');
            }
        });
    });
    
    // Initial load
    loadDashboardData();
    loadUserPreferences();
    
});
</script>

<?php
get_footer();
?>
