<?php
/**
 * Custom search form template
 * 
 * @package EpicNovelTheme
 */
?>

<form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
    <div class="search-form-wrapper">
        <label for="search-field-<?php echo uniqid(); ?>" class="screen-reader-text">
            <?php _e('Search for:', 'epic-novel-theme'); ?>
        </label>
        
        <input type="search" 
               id="search-field-<?php echo uniqid(); ?>" 
               class="search-field" 
               placeholder="<?php _e('Search novels, chapters...', 'epic-novel-theme'); ?>" 
               value="<?php echo get_search_query(); ?>" 
               name="s" 
               autocomplete="off" />
        
        <button type="submit" class="search-submit" aria-label="<?php _e('Search', 'epic-novel-theme'); ?>">
            <i class="fas fa-search"></i>
            <span class="search-text"><?php _e('Search', 'epic-novel-theme'); ?></span>
        </button>
    </div>
    
    <div class="search-results" style="display: none;">
        <!-- Live search results will be populated here -->
    </div>
</form>
