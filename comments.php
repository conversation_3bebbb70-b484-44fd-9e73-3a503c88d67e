<?php
/**
 * The template for displaying comments
 * 
 * @package EpicNovelTheme
 */

if (post_password_required()) {
    return;
}
?>

<div id="comments" class="comments-area">
    
    <?php if (have_comments()) : ?>
        <h2 class="comments-title">
            <?php
            $comment_count = get_comments_number();
            if ('1' === $comment_count) {
                printf(
                    _x('One comment on &ldquo;%1$s&rdquo;', 'comments title', 'epic-novel-theme'),
                    '<span>' . get_the_title() . '</span>'
                );
            } else {
                printf(
                    _nx(
                        '%1$s comment on &ldquo;%2$s&rdquo;',
                        '%1$s comments on &ldquo;%2$s&rdquo;',
                        $comment_count,
                        'comments title',
                        'epic-novel-theme'
                    ),
                    number_format_i18n($comment_count),
                    '<span>' . get_the_title() . '</span>'
                );
            }
            ?>
        </h2>

        <?php the_comments_navigation(); ?>

        <ol class="comment-list">
            <?php
            wp_list_comments(array(
                'style'      => 'ol',
                'short_ping' => true,
                'callback'   => 'epic_comment_callback',
            ));
            ?>
        </ol>

        <?php
        the_comments_navigation();

        // If comments are closed and there are comments, let's leave a little note, shall we?
        if (!comments_open()) :
            ?>
            <p class="no-comments"><?php _e('Comments are closed.', 'epic-novel-theme'); ?></p>
            <?php
        endif;

    endif; // Check for have_comments().

    // Comment form
    $comment_form_args = array(
        'title_reply'          => __('Leave a Comment', 'epic-novel-theme'),
        'title_reply_to'       => __('Leave a Reply to %s', 'epic-novel-theme'),
        'title_reply_before'   => '<h3 id="reply-title" class="comment-reply-title">',
        'title_reply_after'    => '</h3>',
        'cancel_reply_before'  => ' <small>',
        'cancel_reply_after'   => '</small>',
        'cancel_reply_link'    => __('Cancel reply', 'epic-novel-theme'),
        'label_submit'         => __('Post Comment', 'epic-novel-theme'),
        'submit_button'        => '<input name="%1$s" type="submit" id="%2$s" class="%3$s" value="%4$s" />',
        'submit_field'         => '<p class="form-submit">%1$s %2$s</p>',
        'format'               => 'xhtml',
        'comment_field'        => '<p class="comment-form-comment"><label for="comment">' . _x('Comment', 'noun', 'epic-novel-theme') . ' <span class="required">*</span></label> <textarea id="comment" name="comment" cols="45" rows="8" maxlength="65525" required="required" placeholder="' . __('Share your thoughts about this chapter...', 'epic-novel-theme') . '"></textarea></p>',
        'must_log_in'          => '<p class="must-log-in">' . sprintf(
            __('You must be <a href="%s">logged in</a> to post a comment.', 'epic-novel-theme'),
            wp_login_url(apply_filters('the_permalink', get_permalink(get_the_ID())))
        ) . '</p>',
        'logged_in_as'         => '<p class="logged-in-as">' . sprintf(
            __('Logged in as <a href="%1$s">%2$s</a>. <a href="%3$s" title="Log out of this account">Log out?</a>', 'epic-novel-theme'),
            get_edit_user_link(),
            $user_identity,
            wp_logout_url(apply_filters('the_permalink', get_permalink(get_the_ID())))
        ) . '</p>',
        'comment_notes_before' => '<p class="comment-notes"><span id="email-notes">' . __('Your email address will not be published.', 'epic-novel-theme') . '</span> ' . __('Required fields are marked <span class="required">*</span>', 'epic-novel-theme') . '</p>',
        'comment_notes_after'  => '',
        'id_form'              => 'commentform',
        'id_submit'            => 'submit',
        'class_form'           => 'comment-form',
        'class_submit'         => 'submit',
        'name_submit'          => 'submit',
        'fields'               => array(
            'author' => '<p class="comment-form-author">' .
                        '<label for="author">' . __('Name', 'epic-novel-theme') . ' <span class="required">*</span></label> ' .
                        '<input id="author" name="author" type="text" value="' . esc_attr($commenter['comment_author']) . '" size="30" maxlength="245" required="required" /></p>',
            'email'  => '<p class="comment-form-email">' .
                        '<label for="email">' . __('Email', 'epic-novel-theme') . ' <span class="required">*</span></label> ' .
                        '<input id="email" name="email" type="email" value="' . esc_attr($commenter['comment_author_email']) . '" size="30" maxlength="100" aria-describedby="email-notes" required="required" /></p>',
            'url'    => '<p class="comment-form-url">' .
                        '<label for="url">' . __('Website', 'epic-novel-theme') . '</label> ' .
                        '<input id="url" name="url" type="url" value="' . esc_attr($commenter['comment_author_url']) . '" size="30" maxlength="200" /></p>',
        ),
    );

    comment_form($comment_form_args);
    ?>

</div>

<?php
/**
 * Custom comment callback function
 */
function epic_comment_callback($comment, $args, $depth) {
    if ('div' === $args['style']) {
        $tag       = 'div';
        $add_below = 'comment';
    } else {
        $tag       = 'li';
        $add_below = 'div-comment';
    }
    ?>
    <<?php echo $tag; ?> <?php comment_class(empty($args['has_children']) ? '' : 'parent'); ?> id="comment-<?php comment_ID(); ?>">
    
    <?php if ('div' !== $args['style']) : ?>
        <div id="div-comment-<?php comment_ID(); ?>" class="comment-body">
    <?php endif; ?>
    
    <div class="comment-author vcard">
        <?php if ($args['avatar_size'] != 0) echo get_avatar($comment, $args['avatar_size']); ?>
        
        <div class="comment-metadata">
            <?php printf(__('<cite class="fn">%s</cite>', 'epic-novel-theme'), get_comment_author_link()); ?>
            
            <div class="comment-meta commentmetadata">
                <a href="<?php echo htmlspecialchars(get_comment_link($comment->comment_ID)); ?>">
                    <?php
                    printf(
                        __('%1$s at %2$s', 'epic-novel-theme'),
                        get_comment_date(),
                        get_comment_time()
                    );
                    ?>
                </a>
                
                <?php edit_comment_link(__('(Edit)', 'epic-novel-theme'), '  ', ''); ?>
            </div>
        </div>
    </div>

    <?php if ($comment->comment_approved == '0') : ?>
        <em class="comment-awaiting-moderation"><?php _e('Your comment is awaiting moderation.', 'epic-novel-theme'); ?></em>
        <br />
    <?php endif; ?>

    <div class="comment-content">
        <?php comment_text(); ?>
    </div>

    <div class="comment-actions">
        <?php
        comment_reply_link(array_merge($args, array(
            'add_below' => $add_below,
            'depth'     => $depth,
            'max_depth' => $args['max_depth'],
            'reply_text' => __('Reply', 'epic-novel-theme'),
        )));
        ?>
        
        <?php if (is_user_logged_in()) : ?>
            <button class="comment-like-btn" data-comment-id="<?php comment_ID(); ?>">
                <i class="fas fa-heart"></i>
                <span class="like-count">0</span>
            </button>
        <?php endif; ?>
    </div>

    <?php if ('div' !== $args['style']) : ?>
        </div>
    <?php endif; ?>
    
    <?php
}
?>
