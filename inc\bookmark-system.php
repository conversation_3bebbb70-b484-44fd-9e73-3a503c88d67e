<?php
/**
 * Bookmark System Functions
 * 
 * @package EpicNovelTheme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add bookmark button to content
 */
function epic_add_bookmark_button($content) {
    if (is_single() && is_user_logged_in()) {
        $post_id = get_the_ID();
        $user_id = get_current_user_id();
        $is_bookmarked = epic_is_bookmarked($user_id, $post_id);
        
        $bookmark_class = $is_bookmarked ? 'bookmarked' : '';
        $bookmark_text = $is_bookmarked ? __('Remove Bookmark', 'epic-novel-theme') : __('Add Bookmark', 'epic-novel-theme');
        $bookmark_icon = $is_bookmarked ? 'fas fa-bookmark' : 'far fa-bookmark';
        
        $bookmark_button = '<div class="bookmark-container">
            <button class="bookmark-btn ' . $bookmark_class . '" data-post-id="' . $post_id . '" aria-label="' . esc_attr($bookmark_text) . '">
                <i class="' . $bookmark_icon . '"></i>
                <span class="bookmark-text">' . $bookmark_text . '</span>
            </button>
        </div>';
        
        $content .= $bookmark_button;
    }
    
    return $content;
}
add_filter('the_content', 'epic_add_bookmark_button');

/**
 * AJAX handler for adding/removing bookmarks
 */
function epic_ajax_toggle_bookmark() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $user_id = get_current_user_id();
    $post_id = intval($_POST['post_id']);
    $notes = sanitize_textarea_field($_POST['notes'] ?? '');
    
    if (!$post_id) {
        wp_send_json_error('Invalid post ID');
        return;
    }
    
    // Check if already bookmarked
    $is_bookmarked = epic_is_bookmarked($user_id, $post_id);
    
    if ($is_bookmarked) {
        // Remove bookmark
        $result = epic_remove_bookmark($user_id, $post_id);
        if ($result) {
            wp_send_json_success(array(
                'action' => 'removed',
                'message' => __('Bookmark removed', 'epic-novel-theme'),
                'is_bookmarked' => false
            ));
        } else {
            wp_send_json_error('Failed to remove bookmark');
        }
    } else {
        // Add bookmark
        $result = epic_add_bookmark($user_id, $post_id, $notes);
        if ($result) {
            wp_send_json_success(array(
                'action' => 'added',
                'message' => __('Bookmark added', 'epic-novel-theme'),
                'is_bookmarked' => true
            ));
        } else {
            wp_send_json_error('Failed to add bookmark');
        }
    }
}
add_action('wp_ajax_epic_toggle_bookmark', 'epic_ajax_toggle_bookmark');

/**
 * AJAX handler for getting user bookmarks
 */
function epic_ajax_get_bookmarks() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $user_id = get_current_user_id();
    $post_type = sanitize_text_field($_POST['post_type'] ?? 'all');
    $page = intval($_POST['page'] ?? 1);
    $per_page = 20;
    $offset = ($page - 1) * $per_page;
    
    $bookmarks = epic_get_user_bookmarks($user_id, $post_type, $per_page);
    
    $formatted_bookmarks = array();
    foreach ($bookmarks as $bookmark) {
        $post = get_post($bookmark->post_id);
        if ($post) {
            $formatted_bookmarks[] = array(
                'id' => $bookmark->id,
                'post_id' => $bookmark->post_id,
                'post_title' => $post->post_title,
                'post_url' => get_permalink($post->ID),
                'post_type' => $bookmark->post_type,
                'bookmark_date' => $bookmark->bookmark_date,
                'notes' => $bookmark->notes,
                'excerpt' => get_the_excerpt($post->ID),
                'thumbnail' => get_the_post_thumbnail_url($post->ID, 'thumbnail')
            );
        }
    }
    
    wp_send_json_success(array(
        'bookmarks' => $formatted_bookmarks,
        'page' => $page,
        'has_more' => count($bookmarks) === $per_page
    ));
}
add_action('wp_ajax_epic_get_bookmarks', 'epic_ajax_get_bookmarks');

/**
 * Create bookmarks page template
 */
function epic_create_bookmarks_page() {
    // Check if bookmarks page exists
    $bookmarks_page = get_page_by_path('bookmarks');
    
    if (!$bookmarks_page) {
        // Create bookmarks page
        $page_data = array(
            'post_title' => __('My Bookmarks', 'epic-novel-theme'),
            'post_content' => '[epic_bookmarks]',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'bookmarks'
        );
        
        wp_insert_post($page_data);
    }
}
add_action('after_switch_theme', 'epic_create_bookmarks_page');

/**
 * Bookmarks shortcode
 */
function epic_bookmarks_shortcode($atts) {
    if (!is_user_logged_in()) {
        return '<p>' . __('Please log in to view your bookmarks.', 'epic-novel-theme') . '</p>';
    }
    
    $atts = shortcode_atts(array(
        'type' => 'all',
        'per_page' => 20
    ), $atts);
    
    ob_start();
    ?>
    
    <div class="bookmarks-container">
        <div class="bookmarks-header">
            <h2><?php _e('My Bookmarks', 'epic-novel-theme'); ?></h2>
            
            <div class="bookmark-filters">
                <select id="bookmark-type-filter">
                    <option value="all"><?php _e('All Bookmarks', 'epic-novel-theme'); ?></option>
                    <option value="novel"><?php _e('Novels', 'epic-novel-theme'); ?></option>
                    <option value="post"><?php _e('Chapters', 'epic-novel-theme'); ?></option>
                </select>
                
                <button class="refresh-bookmarks" id="refresh-bookmarks">
                    <i class="fas fa-sync-alt"></i>
                    <?php _e('Refresh', 'epic-novel-theme'); ?>
                </button>
            </div>
        </div>
        
        <div class="bookmarks-list" id="bookmarks-list">
            <div class="loading-bookmarks">
                <div class="spinner"></div>
                <p><?php _e('Loading bookmarks...', 'epic-novel-theme'); ?></p>
            </div>
        </div>
        
        <div class="bookmarks-pagination" id="bookmarks-pagination" style="display: none;">
            <button class="load-more-bookmarks" id="load-more-bookmarks">
                <?php _e('Load More', 'epic-novel-theme'); ?>
            </button>
        </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        let currentPage = 1;
        let currentType = 'all';
        let hasMore = true;
        
        function loadBookmarks(page = 1, type = 'all', append = false) {
            $.ajax({
                url: epic_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'epic_get_bookmarks',
                    nonce: epic_ajax.nonce,
                    page: page,
                    post_type: type
                },
                success: function(response) {
                    if (response.success) {
                        const bookmarks = response.data.bookmarks;
                        hasMore = response.data.has_more;
                        
                        let html = '';
                        bookmarks.forEach(function(bookmark) {
                            html += `
                                <div class="bookmark-item" data-bookmark-id="${bookmark.id}">
                                    <div class="bookmark-content">
                                        <h3><a href="${bookmark.post_url}">${bookmark.post_title}</a></h3>
                                        <p class="bookmark-meta">
                                            <span class="bookmark-type">${bookmark.post_type}</span>
                                            <span class="bookmark-date">${new Date(bookmark.bookmark_date).toLocaleDateString()}</span>
                                        </p>
                                        ${bookmark.excerpt ? `<p class="bookmark-excerpt">${bookmark.excerpt}</p>` : ''}
                                        ${bookmark.notes ? `<p class="bookmark-notes"><strong>Notes:</strong> ${bookmark.notes}</p>` : ''}
                                    </div>
                                    <div class="bookmark-actions">
                                        <a href="${bookmark.post_url}" class="read-btn">
                                            <i class="fas fa-book-open"></i>
                                            <?php _e('Read', 'epic-novel-theme'); ?>
                                        </a>
                                        <button class="remove-bookmark" data-post-id="${bookmark.post_id}">
                                            <i class="fas fa-trash"></i>
                                            <?php _e('Remove', 'epic-novel-theme'); ?>
                                        </button>
                                    </div>
                                </div>
                            `;
                        });
                        
                        if (append) {
                            $('#bookmarks-list').append(html);
                        } else {
                            $('#bookmarks-list').html(html);
                        }
                        
                        // Show/hide pagination
                        if (hasMore) {
                            $('#bookmarks-pagination').show();
                        } else {
                            $('#bookmarks-pagination').hide();
                        }
                        
                        if (bookmarks.length === 0 && page === 1) {
                            $('#bookmarks-list').html('<p class="no-bookmarks"><?php _e('No bookmarks found.', 'epic-novel-theme'); ?></p>');
                        }
                    } else {
                        $('#bookmarks-list').html('<p class="error">' + response.data + '</p>');
                    }
                },
                error: function() {
                    $('#bookmarks-list').html('<p class="error"><?php _e('Failed to load bookmarks.', 'epic-novel-theme'); ?></p>');
                }
            });
        }
        
        // Initial load
        loadBookmarks();
        
        // Filter change
        $('#bookmark-type-filter').on('change', function() {
            currentType = $(this).val();
            currentPage = 1;
            loadBookmarks(currentPage, currentType);
        });
        
        // Load more
        $('#load-more-bookmarks').on('click', function() {
            currentPage++;
            loadBookmarks(currentPage, currentType, true);
        });
        
        // Refresh
        $('#refresh-bookmarks').on('click', function() {
            currentPage = 1;
            loadBookmarks(currentPage, currentType);
        });
        
        // Remove bookmark
        $(document).on('click', '.remove-bookmark', function() {
            const postId = $(this).data('post-id');
            const bookmarkItem = $(this).closest('.bookmark-item');
            
            if (confirm('<?php _e('Are you sure you want to remove this bookmark?', 'epic-novel-theme'); ?>')) {
                $.ajax({
                    url: epic_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'epic_toggle_bookmark',
                        nonce: epic_ajax.nonce,
                        post_id: postId
                    },
                    success: function(response) {
                        if (response.success) {
                            bookmarkItem.fadeOut(300, function() {
                                $(this).remove();
                            });
                        } else {
                            alert('<?php _e('Failed to remove bookmark.', 'epic-novel-theme'); ?>');
                        }
                    }
                });
            }
        });
    });
    </script>
    
    <?php
    return ob_get_clean();
}
add_shortcode('epic_bookmarks', 'epic_bookmarks_shortcode');

/**
 * Add bookmark count to admin columns
 */
function epic_add_bookmark_columns($columns) {
    $columns['bookmarks'] = __('Bookmarks', 'epic-novel-theme');
    return $columns;
}
add_filter('manage_posts_columns', 'epic_add_bookmark_columns');
add_filter('manage_novel_posts_columns', 'epic_add_bookmark_columns');

/**
 * Display bookmark count in admin columns
 */
function epic_display_bookmark_count($column, $post_id) {
    if ($column === 'bookmarks') {
        global $wpdb;
        $bookmarks_table = $wpdb->prefix . 'epic_bookmarks';
        
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $bookmarks_table WHERE post_id = %d",
            $post_id
        ));
        
        echo intval($count);
    }
}
add_action('manage_posts_custom_column', 'epic_display_bookmark_count', 10, 2);
add_action('manage_novel_posts_custom_column', 'epic_display_bookmark_count', 10, 2);
