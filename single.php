<?php
/**
 * The template for displaying single posts (chapters)
 * 
 * @package EpicNovelTheme
 */

get_header(); ?>

<div class="reading-container">
    <main id="main" class="site-main">
        
        <?php while (have_posts()) : the_post(); ?>
            
            <article id="post-<?php the_ID(); ?>" <?php post_class('chapter-single'); ?>>
                
                <header class="chapter-header">
                    <?php
                    $novel_title = get_post_meta(get_the_ID(), 'novel_title', true);
                    $chapter_number = get_post_meta(get_the_ID(), 'chapter_number', true);
                    $volume_title = get_post_meta(get_the_ID(), 'volume_title', true);
                    $chapter_title = get_post_meta(get_the_ID(), 'chapter_title', true);
                    ?>
                    
                    <?php if ($novel_title) : ?>
                        <div class="novel-breadcrumb">
                            <a href="<?php echo home_url(); ?>"><?php _e('Home', 'epic-novel-theme'); ?></a>
                            <span class="separator">/</span>
                            <a href="<?php echo get_post_type_archive_link('novel'); ?>"><?php _e('Novels', 'epic-novel-theme'); ?></a>
                            <span class="separator">/</span>
                            <?php
                            $novel = get_page_by_title($novel_title, OBJECT, 'novel');
                            if ($novel) :
                            ?>
                                <a href="<?php echo get_permalink($novel->ID); ?>"><?php echo esc_html($novel_title); ?></a>
                            <?php else : ?>
                                <span><?php echo esc_html($novel_title); ?></span>
                            <?php endif; ?>
                            <span class="separator">/</span>
                            <span class="current"><?php _e('Chapter', 'epic-novel-theme'); ?> <?php echo esc_html($chapter_number); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <h1 class="chapter-title">
                        <?php if ($novel_title && $chapter_number) : ?>
                            <?php echo esc_html($novel_title); ?>
                            <?php if ($volume_title) : ?>
                                - <?php echo esc_html($volume_title); ?>
                            <?php endif; ?>
                            - <?php _e('Chapter', 'epic-novel-theme'); ?> <?php echo esc_html($chapter_number); ?>
                            <?php if ($chapter_title) : ?>
                                : <?php echo esc_html($chapter_title); ?>
                            <?php elseif (get_the_title()) : ?>
                                : <?php the_title(); ?>
                            <?php endif; ?>
                        <?php else : ?>
                            <?php the_title(); ?>
                        <?php endif; ?>
                    </h1>
                    
                    <div class="chapter-meta">
                        <div class="meta-item">
                            <i class="fas fa-calendar"></i>
                            <time datetime="<?php echo get_the_date('c'); ?>">
                                <?php echo get_the_date(); ?>
                            </time>
                        </div>
                        
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <?php echo epic_get_reading_time(get_the_ID()); ?> <?php _e('min read', 'epic-novel-theme'); ?>
                        </div>
                        
                        <?php if (is_user_logged_in()) : ?>
                            <div class="meta-item">
                                <button class="bookmark-btn <?php echo epic_is_bookmarked(get_current_user_id(), get_the_ID()) ? 'bookmarked' : ''; ?>" 
                                        data-post-id="<?php the_ID(); ?>">
                                    <i class="fas fa-bookmark"></i>
                                    <?php _e('Bookmark', 'epic-novel-theme'); ?>
                                </button>
                            </div>
                        <?php endif; ?>
                        
                        <div class="meta-item">
                            <button class="share-btn" data-url="<?php the_permalink(); ?>" data-title="<?php the_title_attribute(); ?>">
                                <i class="fas fa-share-alt"></i>
                                <?php _e('Share', 'epic-novel-theme'); ?>
                            </button>
                        </div>
                    </div>
                </header>

                <!-- Ad Space - Header -->
                <div class="ad-space header-ad">
                    <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
                </div>

                <!-- Chapter Navigation - Top -->
                <nav class="chapter-navigation chapter-nav-top">
                    <?php
                    $prev_chapter = epic_get_previous_chapter(get_the_ID());
                    $next_chapter = epic_get_next_chapter(get_the_ID());
                    ?>
                    
                    <div class="nav-previous">
                        <?php if ($prev_chapter) : ?>
                            <a href="<?php echo get_permalink($prev_chapter->ID); ?>" class="nav-btn prev-btn">
                                <i class="fas fa-chevron-left"></i>
                                <span class="nav-text">
                                    <span class="nav-label"><?php _e('Previous Chapter', 'epic-novel-theme'); ?></span>
                                    <span class="nav-title"><?php echo esc_html($prev_chapter->post_title); ?></span>
                                </span>
                            </a>
                        <?php else : ?>
                            <span class="nav-btn prev-btn disabled">
                                <i class="fas fa-chevron-left"></i>
                                <span class="nav-text">
                                    <span class="nav-label"><?php _e('No Previous Chapter', 'epic-novel-theme'); ?></span>
                                </span>
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="nav-center">
                        <?php if ($novel && $novel_title) : ?>
                            <a href="<?php echo get_permalink($novel->ID); ?>" class="novel-link-btn">
                                <i class="fas fa-book"></i>
                                <?php _e('Novel Info', 'epic-novel-theme'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                    
                    <div class="nav-next">
                        <?php if ($next_chapter) : ?>
                            <a href="<?php echo get_permalink($next_chapter->ID); ?>" class="nav-btn next-btn">
                                <span class="nav-text">
                                    <span class="nav-label"><?php _e('Next Chapter', 'epic-novel-theme'); ?></span>
                                    <span class="nav-title"><?php echo esc_html($next_chapter->post_title); ?></span>
                                </span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php else : ?>
                            <span class="nav-btn next-btn disabled">
                                <span class="nav-text">
                                    <span class="nav-label"><?php _e('No Next Chapter', 'epic-novel-theme'); ?></span>
                                </span>
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        <?php endif; ?>
                    </div>
                </nav>

                <div class="chapter-content">
                    <?php the_content(); ?>
                    
                    <?php
                    $translator_notes = get_post_meta(get_the_ID(), 'translator_notes', true);
                    if ($translator_notes) :
                    ?>
                        <div class="translator-notes">
                            <h3><?php _e('Translator Notes', 'epic-novel-theme'); ?></h3>
                            <div class="notes-content">
                                <?php echo wp_kses_post(wpautop($translator_notes)); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Ad Space - Mid Content -->
                <div class="ad-space mid-content-ad">
                    <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
                </div>

                <!-- Chapter Navigation - Bottom -->
                <nav class="chapter-navigation chapter-nav-bottom">
                    <div class="nav-previous">
                        <?php if ($prev_chapter) : ?>
                            <a href="<?php echo get_permalink($prev_chapter->ID); ?>" class="nav-btn prev-btn">
                                <i class="fas fa-chevron-left"></i>
                                <span class="nav-text">
                                    <span class="nav-label"><?php _e('Previous Chapter', 'epic-novel-theme'); ?></span>
                                    <span class="nav-title"><?php echo esc_html($prev_chapter->post_title); ?></span>
                                </span>
                            </a>
                        <?php endif; ?>
                    </div>
                    
                    <div class="nav-center">
                        <?php if ($novel && $novel_title) : ?>
                            <a href="<?php echo get_permalink($novel->ID); ?>" class="novel-link-btn">
                                <i class="fas fa-list"></i>
                                <?php _e('Chapter List', 'epic-novel-theme'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                    
                    <div class="nav-next">
                        <?php if ($next_chapter) : ?>
                            <a href="<?php echo get_permalink($next_chapter->ID); ?>" class="nav-btn next-btn">
                                <span class="nav-text">
                                    <span class="nav-label"><?php _e('Next Chapter', 'epic-novel-theme'); ?></span>
                                    <span class="nav-title"><?php echo esc_html($next_chapter->post_title); ?></span>
                                </span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </nav>

                <footer class="chapter-footer">
                    <div class="chapter-tags">
                        <?php
                        $tags = get_the_tags();
                        if ($tags) :
                        ?>
                            <div class="tags-list">
                                <span class="tags-label"><?php _e('Tags:', 'epic-novel-theme'); ?></span>
                                <?php foreach ($tags as $tag) : ?>
                                    <a href="<?php echo get_tag_link($tag->term_id); ?>" class="tag-link">
                                        <?php echo esc_html($tag->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="chapter-actions">
                        <div class="social-share">
                            <span class="share-label"><?php _e('Share:', 'epic-novel-theme'); ?></span>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" 
                               target="_blank" class="share-twitter" aria-label="<?php _e('Share on Twitter', 'epic-novel-theme'); ?>">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" 
                               target="_blank" class="share-facebook" aria-label="<?php _e('Share on Facebook', 'epic-novel-theme'); ?>">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://www.reddit.com/submit?url=<?php echo urlencode(get_permalink()); ?>&title=<?php echo urlencode(get_the_title()); ?>" 
                               target="_blank" class="share-reddit" aria-label="<?php _e('Share on Reddit', 'epic-novel-theme'); ?>">
                                <i class="fab fa-reddit-alien"></i>
                            </a>
                            <button class="copy-link" data-url="<?php the_permalink(); ?>" aria-label="<?php _e('Copy link', 'epic-novel-theme'); ?>">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                    </div>
                </footer>
                
            </article>
            
        <?php endwhile; ?>

        <!-- Ad Space - Footer -->
        <div class="ad-space footer-ad">
            <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
        </div>

        <!-- Comments Section -->
        <?php
        if (comments_open() || get_comments_number()) :
            comments_template();
        endif;
        ?>

    </main>
</div>

<script>
jQuery(document).ready(function($) {
    // Copy link functionality
    $('.copy-link').on('click', function() {
        const url = $(this).data('url');
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(function() {
                alert('<?php _e('Link copied to clipboard!', 'epic-novel-theme'); ?>');
            });
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = url;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('<?php _e('Link copied to clipboard!', 'epic-novel-theme'); ?>');
        }
    });
    
    // Keyboard navigation
    $(document).on('keydown', function(e) {
        // Left arrow key - previous chapter
        if (e.keyCode === 37) {
            const prevLink = $('.nav-previous a.nav-btn');
            if (prevLink.length) {
                window.location.href = prevLink.attr('href');
            }
        }
        
        // Right arrow key - next chapter
        if (e.keyCode === 39) {
            const nextLink = $('.nav-next a.nav-btn');
            if (nextLink.length) {
                window.location.href = nextLink.attr('href');
            }
        }
    });
    
    // Update view count
    <?php if (is_user_logged_in()) : ?>
    // Track reading progress
    let startTime = Date.now();
    let hasScrolled = false;
    
    $(window).on('scroll', function() {
        if (!hasScrolled) {
            hasScrolled = true;
            
            // Update reading history after user starts reading
            setTimeout(function() {
                const readingTime = Math.round((Date.now() - startTime) / 1000);
                
                $.ajax({
                    url: epic_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'epic_update_reading_progress',
                        nonce: epic_ajax.nonce,
                        post_id: <?php echo get_the_ID(); ?>,
                        progress: 10, // Initial progress
                        reading_time: readingTime
                    }
                });
            }, 5000); // Wait 5 seconds before tracking
        }
    });
    <?php endif; ?>
});
</script>

<?php
// Update novel view count
if ($novel_title) {
    $novel = get_page_by_title($novel_title, OBJECT, 'novel');
    if ($novel) {
        epic_update_novel_view_count($novel->ID);
    }
}

get_footer();
?>
