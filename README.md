# Epic Novel Theme

A comprehensive WordPress theme designed specifically for light novel and web novel websites. Features advanced user management, reading progress tracking, bookmarking system, and Google OAuth integration.

## Features

### Core Features
- **Custom Post Types**: Novels and Chapters with detailed metadata
- **Advanced Taxonomies**: Genres, Writers, Artists, Types, and Tags
- **User Authentication**: Google OAuth integration for seamless login
- **Reading Progress**: Track user reading progress and history
- **Bookmark System**: Save favorite novels and chapters
- **Night Mode**: Dark theme toggle for comfortable reading
- **Responsive Design**: Mobile-first design with touch-friendly controls
- **SEO Optimized**: Schema markup and meta tags for better search visibility

### User Features
- **User Dashboard**: Personalized reading statistics and preferences
- **Continue Reading**: Resume where you left off
- **Reading History**: Track all read chapters with progress
- **Bookmarks**: Save and organize favorite content
- **Reading Controls**: Adjustable font size and reading mode
- **Social Sharing**: Share chapters on social media

### Admin Features
- **Custom Fields**: Rich metadata for novels and chapters
- **Statistics Dashboard**: View site-wide reading statistics
- **User Management**: Enhanced user roles and capabilities
- **Content Organization**: Advanced filtering and sorting options

## Installation

### Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

### Setup Instructions

1. **Download and Install**
   ```bash
   # Upload the theme to your WordPress themes directory
   wp-content/themes/epic-novel-theme/
   ```

2. **Activate the Theme**
   - Go to WordPress Admin → Appearance → Themes
   - Find "Epic Novel Theme" and click "Activate"

3. **Configure Google OAuth (Optional)**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google+ API
   - Create OAuth 2.0 credentials
   - Add your domain to authorized redirect URIs
   - Go to WordPress Admin → Settings → Google OAuth
   - Enter your Client ID and Client Secret
   - Enable Google Login

4. **Set Up Menus**
   - Go to WordPress Admin → Appearance → Menus
   - Create a new menu and assign it to "Primary Menu"
   - Add pages like Home, Novels, Genres, etc.

5. **Configure Widgets**
   - Go to WordPress Admin → Appearance → Widgets
   - Add widgets to Footer Widget areas

## Usage

### Adding Novels

1. **Create a Novel**
   - Go to WordPress Admin → Novels → Add New
   - Fill in novel details:
     - Title and alternative title
     - Description/summary
     - Cover image
     - Status (Ongoing, Completed, Hiatus, Dropped)
     - Release year and language
     - Rating and difficulty level
   - Assign genres, writers, artists, and tags
   - Publish the novel

2. **Add Chapters**
   - Go to WordPress Admin → Posts → Add New
   - Write your chapter content
   - In "Chapter Details" meta box:
     - Select the novel title
     - Enter chapter number
     - Add volume title (optional)
     - Add translator notes (optional)
   - Publish the chapter

### User Management

**User Roles:**
- **Subscriber**: Can read, bookmark, and comment
- **Translator**: Can create and edit chapters
- **Novel Author**: Can create novels and chapters
- **Editor**: Can manage all content
- **Administrator**: Full access

### Customization

**Theme Options:**
- Go to WordPress Admin → Appearance → Customize
- Configure:
  - Site identity and logo
  - Colors and typography
  - Google OAuth settings
  - Footer widgets

**Custom CSS:**
- Add custom styles in Appearance → Customize → Additional CSS
- The theme supports CSS custom properties for easy theming

## File Structure

```
epic-novel-theme/
├── style.css                 # Main stylesheet
├── index.php                 # Main template
├── functions.php             # Theme functions
├── header.php                # Header template
├── footer.php                # Footer template
├── sidebar.php               # Sidebar template
├── single.php                # Single post (chapter) template
├── single-novel.php          # Single novel template
├── archive-novel.php         # Novel archive template
├── page-dashboard.php        # User dashboard template
├── searchform.php            # Search form template
├── comments.php              # Comments template
├── assets/
│   └── js/
│       └── main.js           # Main JavaScript file
└── inc/
    ├── custom-fields.php     # Custom meta boxes and fields
    ├── user-functions.php    # User management functions
    ├── bookmark-system.php   # Bookmark functionality
    ├── reading-history.php   # Reading progress tracking
    ├── google-oauth.php      # Google OAuth integration
    ├── ajax-handlers.php     # AJAX request handlers
    ├── seo-functions.php     # SEO and schema markup
    └── admin-functions.php   # Admin customizations
```

## Database Tables

The theme creates custom tables for enhanced functionality:

- `wp_epic_bookmarks` - User bookmarks
- `wp_epic_reading_history` - Reading progress tracking
- `wp_epic_user_preferences` - User preferences
- `wp_epic_novel_stats` - Novel statistics

## Hooks and Filters

### Actions
- `epic_before_chapter_content` - Before chapter content
- `epic_after_chapter_content` - After chapter content
- `epic_novel_sidebar` - Novel sidebar content

### Filters
- `epic_reading_time` - Modify reading time calculation
- `epic_novel_meta` - Customize novel metadata display
- `epic_chapter_navigation` - Customize chapter navigation

## Shortcodes

- `[epic_bookmarks]` - Display user bookmarks
- `[epic_reading_history]` - Display reading history
- `[epic_novel_list]` - Display novel list with filters
- `[epic_recent_chapters]` - Display recent chapters

## JavaScript API

The theme provides a JavaScript API for custom functionality:

```javascript
// Access theme functions
EpicTheme.showNotification('Message', 'success');
EpicTheme.updateFontSize(120);

// Utility functions
EpicTheme.utils.formatNumber(1500); // "1.5K"
EpicTheme.utils.getReadingTime(text);
```

## Troubleshooting

### Common Issues

1. **Google OAuth not working**
   - Check Client ID and Secret
   - Verify authorized redirect URIs
   - Ensure Google+ API is enabled

2. **Reading progress not saving**
   - Check if user is logged in
   - Verify database tables were created
   - Check JavaScript console for errors

3. **Styles not loading**
   - Clear cache
   - Check file permissions
   - Verify theme is properly activated

### Debug Mode

Enable WordPress debug mode to troubleshoot issues:

```php
// In wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This theme is licensed under the GPL v2 or later.

## Support

For support and questions:
- Check the documentation
- Search existing issues
- Create a new issue with detailed information

## Changelog

### Version 1.0.0
- Initial release
- Core novel and chapter management
- User authentication and progress tracking
- Responsive design with night mode
- SEO optimization and schema markup
- Google OAuth integration
- Bookmark and reading history system
