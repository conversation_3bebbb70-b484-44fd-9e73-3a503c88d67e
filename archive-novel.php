<?php
/**
 * The template for displaying novel archives
 * 
 * @package EpicNovelTheme
 */

get_header(); ?>

<div class="container">
    <main id="main" class="site-main">
        
        <header class="page-header">
            <h1 class="page-title"><?php _e('Novels', 'epic-novel-theme'); ?></h1>
            <p class="archive-description"><?php _e('Browse our collection of light novels and web novels.', 'epic-novel-theme'); ?></p>
        </header>

        <!-- Ad Space - Header -->
        <div class="ad-space header-ad">
            <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
        </div>

        <div class="novels-archive-content">
            
            <!-- Novels Grid -->
            <div class="novels-grid" id="novels-grid">
                <?php if (have_posts()) : ?>
                    <?php while (have_posts()) : the_post(); ?>
                        
                        <article id="novel-<?php the_ID(); ?>" <?php post_class('novel-card'); ?>>
                            
                            <div class="novel-card-cover">
                                <?php if (has_post_thumbnail()) : ?>
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('novel-thumbnail'); ?>
                                    </a>
                                <?php else : ?>
                                    <a href="<?php the_permalink(); ?>" class="novel-placeholder">
                                        <i class="fas fa-book"></i>
                                    </a>
                                <?php endif; ?>
                                
                                <?php if (is_user_logged_in()) : ?>
                                    <button class="bookmark-btn <?php echo epic_is_bookmarked(get_current_user_id(), get_the_ID()) ? 'bookmarked' : ''; ?>" 
                                            data-post-id="<?php the_ID(); ?>" 
                                            aria-label="<?php _e('Bookmark this novel', 'epic-novel-theme'); ?>">
                                        <i class="fas fa-bookmark"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                            
                            <div class="novel-card-content">
                                <h2 class="novel-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h2>
                                
                                <?php
                                $alternative_title = get_post_meta(get_the_ID(), 'alternative_title', true);
                                if ($alternative_title) :
                                ?>
                                    <p class="alternative-title"><?php echo esc_html($alternative_title); ?></p>
                                <?php endif; ?>
                                
                                <div class="novel-meta">
                                    <?php
                                    $writers = get_the_terms(get_the_ID(), 'writer');
                                    if ($writers && !is_wp_error($writers)) :
                                    ?>
                                        <div class="novel-writers">
                                            <i class="fas fa-user"></i>
                                            <?php
                                            $writer_names = wp_list_pluck($writers, 'name');
                                            echo esc_html(implode(', ', $writer_names));
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php
                                    $novel_status = get_post_meta(get_the_ID(), 'novel_status', true);
                                    if ($novel_status) :
                                    ?>
                                        <div class="novel-status">
                                            <span class="status-badge status-<?php echo esc_attr($novel_status); ?>">
                                                <?php
                                                $status_labels = array(
                                                    'ongoing' => __('Ongoing', 'epic-novel-theme'),
                                                    'completed' => __('Completed', 'epic-novel-theme'),
                                                    'hiatus' => __('Hiatus', 'epic-novel-theme'),
                                                    'dropped' => __('Dropped', 'epic-novel-theme')
                                                );
                                                echo esc_html($status_labels[$novel_status] ?? $novel_status);
                                                ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php
                                    $rating = get_post_meta(get_the_ID(), 'rating', true);
                                    if ($rating) :
                                    ?>
                                        <div class="novel-rating">
                                            <div class="stars">
                                                <?php
                                                $full_stars = floor($rating / 2);
                                                $half_star = ($rating / 2) - $full_stars >= 0.5;
                                                
                                                for ($i = 1; $i <= 5; $i++) {
                                                    if ($i <= $full_stars) {
                                                        echo '<i class="fas fa-star"></i>';
                                                    } elseif ($i == $full_stars + 1 && $half_star) {
                                                        echo '<i class="fas fa-star-half-alt"></i>';
                                                    } else {
                                                        echo '<i class="far fa-star"></i>';
                                                    }
                                                }
                                                ?>
                                            </div>
                                            <span class="rating-text"><?php echo number_format($rating, 1); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="novel-description">
                                    <?php
                                    $novel_summary = get_post_meta(get_the_ID(), 'novel_summary', true);
                                    if ($novel_summary) {
                                        echo wp_trim_words($novel_summary, 20);
                                    } else {
                                        echo wp_trim_words(get_the_excerpt(), 20);
                                    }
                                    ?>
                                </div>
                                
                                <div class="novel-genres">
                                    <?php
                                    $genres = get_the_terms(get_the_ID(), 'genre');
                                    if ($genres && !is_wp_error($genres)) :
                                        $genre_count = 0;
                                        foreach ($genres as $genre) :
                                            if ($genre_count >= 3) break; // Limit to 3 genres
                                    ?>
                                            <a href="<?php echo get_term_link($genre); ?>" class="genre-tag">
                                                <?php echo esc_html($genre->name); ?>
                                            </a>
                                    <?php
                                            $genre_count++;
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                                
                                <div class="novel-stats">
                                    <?php
                                    $view_count = get_post_meta(get_the_ID(), 'view_count', true);
                                    $novel_title = get_the_title();
                                    $chapter_count = get_posts(array(
                                        'post_type' => 'post',
                                        'posts_per_page' => -1,
                                        'meta_query' => array(
                                            array(
                                                'key' => 'novel_title',
                                                'value' => $novel_title,
                                                'compare' => '='
                                            )
                                        ),
                                        'fields' => 'ids'
                                    ));
                                    $chapter_count = count($chapter_count);
                                    ?>
                                    
                                    <span class="stat-item">
                                        <i class="fas fa-eye"></i>
                                        <?php echo $view_count ? number_format($view_count) : '0'; ?>
                                    </span>
                                    
                                    <span class="stat-item">
                                        <i class="fas fa-book-open"></i>
                                        <?php printf(_n('%s chapter', '%s chapters', $chapter_count, 'epic-novel-theme'), number_format($chapter_count)); ?>
                                    </span>
                                </div>
                                
                                <div class="novel-actions">
                                    <a href="<?php the_permalink(); ?>" class="read-novel-btn">
                                        <i class="fas fa-book-open"></i>
                                        <?php _e('Read Novel', 'epic-novel-theme'); ?>
                                    </a>
                                    
                                    <?php if ($chapter_count > 0) : ?>
                                        <?php
                                        // Get latest chapter
                                        $latest_chapter = get_posts(array(
                                            'post_type' => 'post',
                                            'posts_per_page' => 1,
                                            'meta_query' => array(
                                                array(
                                                    'key' => 'novel_title',
                                                    'value' => $novel_title,
                                                    'compare' => '='
                                                )
                                            ),
                                            'meta_key' => 'chapter_number',
                                            'orderby' => 'meta_value_num',
                                            'order' => 'DESC'
                                        ));
                                        
                                        if ($latest_chapter) :
                                        ?>
                                            <a href="<?php echo get_permalink($latest_chapter[0]->ID); ?>" class="latest-chapter-btn">
                                                <i class="fas fa-forward"></i>
                                                <?php _e('Latest Chapter', 'epic-novel-theme'); ?>
                                            </a>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                        </article>
                        
                    <?php endwhile; ?>
                    
                <?php else : ?>
                    
                    <div class="no-novels-found">
                        <h2><?php _e('No novels found', 'epic-novel-theme'); ?></h2>
                        <p><?php _e('Sorry, no novels match your search criteria.', 'epic-novel-theme'); ?></p>
                        <a href="<?php echo get_post_type_archive_link('novel'); ?>" class="reset-filters-btn">
                            <?php _e('View All Novels', 'epic-novel-theme'); ?>
                        </a>
                    </div>
                    
                <?php endif; ?>
            </div>
            
            <!-- Load More Button -->
            <div class="load-more-container" id="load-more-container" style="display: none;">
                <button class="load-more-novels" id="load-more-novels">
                    <span class="load-more-text"><?php _e('Load More Novels', 'epic-novel-theme'); ?></span>
                    <span class="load-more-spinner" style="display: none;">
                        <div class="spinner"></div>
                        <?php _e('Loading...', 'epic-novel-theme'); ?>
                    </span>
                </button>
            </div>
            
            <!-- Pagination -->
            <nav class="pagination-nav">
                <?php
                the_posts_pagination(array(
                    'mid_size' => 2,
                    'prev_text' => '<i class="fas fa-chevron-left"></i> ' . __('Previous', 'epic-novel-theme'),
                    'next_text' => __('Next', 'epic-novel-theme') . ' <i class="fas fa-chevron-right"></i>',
                ));
                ?>
            </nav>
            
        </div>

        <!-- Ad Space - Footer -->
        <div class="ad-space footer-ad">
            <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
        </div>

    </main>
</div>

<script>
jQuery(document).ready(function($) {
    let currentPage = 1;
    let maxPages = <?php echo $GLOBALS['wp_query']->max_num_pages; ?>;
    let loading = false;
    
    // Show load more button if there are more pages
    if (maxPages > 1) {
        $('#load-more-container').show();
    }
    
    // Load more novels
    $('#load-more-novels').on('click', function() {
        if (loading || currentPage >= maxPages) {
            return;
        }
        
        loading = true;
        const button = $(this);
        const loadMoreText = button.find('.load-more-text');
        const loadMoreSpinner = button.find('.load-more-spinner');
        
        loadMoreText.hide();
        loadMoreSpinner.show();
        button.prop('disabled', true);
        
        // Get current filter values
        const urlParams = new URLSearchParams(window.location.search);
        const filterData = {
            action: 'epic_load_more_novels',
            nonce: epic_ajax.nonce,
            page: currentPage + 1,
            posts_per_page: 12,
            genre: urlParams.get('genre') || '',
            status: urlParams.get('status') || '',
            orderby: urlParams.get('orderby') || 'date',
            search: urlParams.get('s') || ''
        };
        
        $.ajax({
            url: epic_ajax.ajax_url,
            type: 'POST',
            data: filterData,
            success: function(response) {
                if (response.success) {
                    const novels = response.data.novels;
                    
                    novels.forEach(function(novel) {
                        let genresHtml = '';
                        if (novel.genres.length > 0) {
                            novel.genres.slice(0, 3).forEach(function(genre) {
                                genresHtml += `<span class="genre-tag">${genre}</span>`;
                            });
                        }
                        
                        let ratingHtml = '';
                        if (novel.rating > 0) {
                            const fullStars = Math.floor(novel.rating / 2);
                            const halfStar = (novel.rating / 2) - fullStars >= 0.5;
                            
                            ratingHtml = '<div class="novel-rating"><div class="stars">';
                            for (let i = 1; i <= 5; i++) {
                                if (i <= fullStars) {
                                    ratingHtml += '<i class="fas fa-star"></i>';
                                } else if (i === fullStars + 1 && halfStar) {
                                    ratingHtml += '<i class="fas fa-star-half-alt"></i>';
                                } else {
                                    ratingHtml += '<i class="far fa-star"></i>';
                                }
                            }
                            ratingHtml += `</div><span class="rating-text">${novel.rating.toFixed(1)}</span></div>`;
                        }
                        
                        const bookmarkClass = novel.is_bookmarked ? 'bookmarked' : '';
                        const bookmarkButton = epic_ajax.user_id ? `<button class="bookmark-btn ${bookmarkClass}" data-post-id="${novel.id}"><i class="fas fa-bookmark"></i></button>` : '';
                        
                        const novelHtml = `
                            <article id="novel-${novel.id}" class="novel-card">
                                <div class="novel-card-cover">
                                    <a href="${novel.url}">
                                        ${novel.thumbnail ? `<img src="${novel.thumbnail}" alt="${novel.title}">` : '<div class="novel-placeholder"><i class="fas fa-book"></i></div>'}
                                    </a>
                                    ${bookmarkButton}
                                </div>
                                <div class="novel-card-content">
                                    <h2 class="novel-title"><a href="${novel.url}">${novel.title}</a></h2>
                                    <div class="novel-meta">
                                        ${novel.writers.length > 0 ? `<div class="novel-writers"><i class="fas fa-user"></i> ${novel.writers.join(', ')}</div>` : ''}
                                        ${novel.status ? `<div class="novel-status"><span class="status-badge status-${novel.status}">${novel.status}</span></div>` : ''}
                                        ${ratingHtml}
                                    </div>
                                    <div class="novel-description">${novel.excerpt}</div>
                                    <div class="novel-genres">${genresHtml}</div>
                                    <div class="novel-stats">
                                        <span class="stat-item"><i class="fas fa-eye"></i> ${novel.view_count.toLocaleString()}</span>
                                    </div>
                                    <div class="novel-actions">
                                        <a href="${novel.url}" class="read-novel-btn"><i class="fas fa-book-open"></i> <?php _e('Read Novel', 'epic-novel-theme'); ?></a>
                                    </div>
                                </div>
                            </article>
                        `;
                        
                        $('#novels-grid').append(novelHtml);
                    });
                    
                    currentPage = response.data.page;
                    
                    if (!response.data.has_more) {
                        $('#load-more-container').hide();
                    }
                } else {
                    alert('<?php _e('Failed to load more novels.', 'epic-novel-theme'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('Failed to load more novels.', 'epic-novel-theme'); ?>');
            },
            complete: function() {
                loading = false;
                loadMoreSpinner.hide();
                loadMoreText.show();
                button.prop('disabled', false);
            }
        });
    });
});
</script>

<?php
get_footer();
?>
