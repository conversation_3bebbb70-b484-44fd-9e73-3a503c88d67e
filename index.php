<?php
/**
 * The main template file - Novel-focused homepage
 *
 * @package EpicNovelTheme
 */

get_header(); ?>

<div class="container">
    <main id="main" class="site-main">

        <header class="page-header">
            <h1 class="page-title"><?php _e('Recently Updated Novels', 'epic-novel-theme'); ?></h1>
            <p class="page-description"><?php _e('Discover the latest chapters from your favorite novels.', 'epic-novel-theme'); ?></p>
        </header>

        <!-- Ad Space - Header -->
        <div class="ad-space header-ad">
            <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
        </div>

        <?php
        // Get novels with recent chapters
        $novels_with_chapters = epic_get_novels_with_recent_chapters();

        if (!empty($novels_with_chapters)) :
        ?>

            <div class="novels-homepage-grid" id="novels-homepage-grid">
                <?php foreach ($novels_with_chapters as $novel_data) :
                    $novel = $novel_data['novel'];
                    $recent_chapters = $novel_data['recent_chapters'];
                    $latest_chapter_date = $novel_data['latest_chapter_date'];
                ?>

                    <article id="novel-<?php echo $novel->ID; ?>" class="novel-homepage-card">

                        <div class="novel-card-cover">
                            <?php if (has_post_thumbnail($novel->ID)) : ?>
                                <a href="<?php echo get_permalink($novel->ID); ?>">
                                    <?php echo get_the_post_thumbnail($novel->ID, 'novel-thumbnail'); ?>
                                </a>
                            <?php else : ?>
                                <a href="<?php echo get_permalink($novel->ID); ?>" class="novel-placeholder">
                                    <i class="fas fa-book"></i>
                                </a>
                            <?php endif; ?>

                            <?php if (is_user_logged_in()) : ?>
                                <button class="bookmark-btn <?php echo epic_is_bookmarked(get_current_user_id(), $novel->ID) ? 'bookmarked' : ''; ?>"
                                        data-post-id="<?php echo $novel->ID; ?>"
                                        aria-label="<?php _e('Bookmark this novel', 'epic-novel-theme'); ?>">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                            <?php endif; ?>

                            <div class="update-badge">
                                <i class="fas fa-clock"></i>
                                <span><?php echo human_time_diff(strtotime($latest_chapter_date), current_time('timestamp')); ?> <?php _e('ago', 'epic-novel-theme'); ?></span>
                            </div>
                        </div>

                        <div class="novel-card-content">
                            <h2 class="novel-title">
                                <a href="<?php echo get_permalink($novel->ID); ?>"><?php echo esc_html($novel->post_title); ?></a>
                            </h2>

                            <?php
                            $alternative_title = get_post_meta($novel->ID, 'alternative_title', true);
                            if ($alternative_title) :
                            ?>
                                <p class="alternative-title"><?php echo esc_html($alternative_title); ?></p>
                            <?php endif; ?>

                            <div class="novel-meta">
                                <?php
                                $writers = get_the_terms($novel->ID, 'writer');
                                if ($writers && !is_wp_error($writers)) :
                                ?>
                                    <div class="novel-writers">
                                        <i class="fas fa-user"></i>
                                        <?php
                                        $writer_names = wp_list_pluck($writers, 'name');
                                        echo esc_html(implode(', ', $writer_names));
                                        ?>
                                    </div>
                                <?php endif; ?>

                                <?php
                                $novel_status = get_post_meta($novel->ID, 'novel_status', true);
                                if ($novel_status) :
                                ?>
                                    <div class="novel-status">
                                        <span class="status-badge status-<?php echo esc_attr($novel_status); ?>">
                                            <?php
                                            $status_labels = array(
                                                'ongoing' => __('Ongoing', 'epic-novel-theme'),
                                                'completed' => __('Completed', 'epic-novel-theme'),
                                                'hiatus' => __('Hiatus', 'epic-novel-theme'),
                                                'dropped' => __('Dropped', 'epic-novel-theme')
                                            );
                                            echo esc_html($status_labels[$novel_status] ?? $novel_status);
                                            ?>
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <?php
                                $rating = get_post_meta($novel->ID, 'rating', true);
                                if ($rating) :
                                ?>
                                    <div class="novel-rating">
                                        <div class="stars">
                                            <?php
                                            $full_stars = floor($rating / 2);
                                            $half_star = ($rating / 2) - $full_stars >= 0.5;

                                            for ($i = 1; $i <= 5; $i++) {
                                                if ($i <= $full_stars) {
                                                    echo '<i class="fas fa-star"></i>';
                                                } elseif ($i == $full_stars + 1 && $half_star) {
                                                    echo '<i class="fas fa-star-half-alt"></i>';
                                                } else {
                                                    echo '<i class="far fa-star"></i>';
                                                }
                                            }
                                            ?>
                                        </div>
                                        <span class="rating-text"><?php echo number_format($rating, 1); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="novel-description">
                                <?php
                                $novel_summary = get_post_meta($novel->ID, 'novel_summary', true);
                                if ($novel_summary) {
                                    echo wp_trim_words($novel_summary, 25);
                                } else {
                                    echo wp_trim_words($novel->post_excerpt ?: $novel->post_content, 25);
                                }
                                ?>
                            </div>

                            <div class="recent-chapters">
                                <h4 class="recent-chapters-title"><?php _e('Latest Chapters', 'epic-novel-theme'); ?></h4>
                                <div class="chapters-list">
                                    <?php foreach ($recent_chapters as $chapter) :
                                        $chapter_number = get_post_meta($chapter->ID, 'chapter_number', true);
                                        $volume_title = get_post_meta($chapter->ID, 'volume_title', true);
                                    ?>
                                        <div class="chapter-item">
                                            <div class="chapter-info">
                                                <h5 class="chapter-title">
                                                    <a href="<?php echo get_permalink($chapter->ID); ?>">
                                                        <?php if ($chapter_number) : ?>
                                                            <?php _e('Chapter', 'epic-novel-theme'); ?> <?php echo esc_html($chapter_number); ?>:
                                                        <?php endif; ?>
                                                        <?php echo esc_html($chapter->post_title); ?>
                                                    </a>
                                                </h5>
                                                <?php if ($volume_title) : ?>
                                                    <p class="volume-title"><?php echo esc_html($volume_title); ?></p>
                                                <?php endif; ?>
                                                <div class="chapter-meta">
                                                    <span class="chapter-date">
                                                        <i class="fas fa-calendar"></i>
                                                        <?php echo get_the_date('M j, Y', $chapter->ID); ?>
                                                    </span>
                                                    <span class="reading-time">
                                                        <i class="fas fa-clock"></i>
                                                        <?php echo epic_get_reading_time($chapter->ID); ?> <?php _e('min', 'epic-novel-theme'); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="chapter-actions">
                                                <a href="<?php echo get_permalink($chapter->ID); ?>" class="read-btn">
                                                    <i class="fas fa-book-open"></i>
                                                    <?php _e('Read', 'epic-novel-theme'); ?>
                                                </a>
                                                <?php if (is_user_logged_in()) : ?>
                                                    <button class="bookmark-btn <?php echo epic_is_bookmarked(get_current_user_id(), $chapter->ID) ? 'bookmarked' : ''; ?>"
                                                            data-post-id="<?php echo $chapter->ID; ?>"
                                                            aria-label="<?php _e('Bookmark chapter', 'epic-novel-theme'); ?>">
                                                        <i class="fas fa-bookmark"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <div class="novel-actions">
                                <a href="<?php echo get_permalink($novel->ID); ?>" class="view-novel-btn">
                                    <i class="fas fa-info-circle"></i>
                                    <?php _e('Novel Info', 'epic-novel-theme'); ?>
                                </a>

                                <?php if (!empty($recent_chapters)) : ?>
                                    <a href="<?php echo get_permalink($recent_chapters[0]->ID); ?>" class="latest-chapter-btn">
                                        <i class="fas fa-forward"></i>
                                        <?php _e('Latest Chapter', 'epic-novel-theme'); ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>

                    </article>

                <?php endforeach; ?>
            </div>

            <!-- Load More Button -->
            <div class="load-more-container" id="load-more-container">
                <button class="load-more-novels" id="load-more-novels" data-page="1">
                    <span class="load-more-text"><?php _e('Load More Novels', 'epic-novel-theme'); ?></span>
                    <span class="load-more-spinner" style="display: none;">
                        <div class="spinner"></div>
                        <?php _e('Loading...', 'epic-novel-theme'); ?>
                    </span>
                </button>
            </div>

        <?php else : ?>

            <section class="no-results not-found">
                <header class="page-header">
                    <h1 class="page-title"><?php _e('No novels found', 'epic-novel-theme'); ?></h1>
                </header>

                <div class="page-content">
                    <?php if (current_user_can('publish_posts')) : ?>
                        <p><?php
                        printf(
                            wp_kses(
                                __('Ready to publish your first novel? <a href="%1$s">Get started here</a>.', 'epic-novel-theme'),
                                array(
                                    'a' => array(
                                        'href' => array(),
                                    ),
                                )
                            ),
                            esc_url(admin_url('post-new.php?post_type=novel'))
                        );
                        ?></p>
                    <?php else : ?>
                        <p><?php _e('No novels with published chapters found. Check back later for updates!', 'epic-novel-theme'); ?></p>
                        <a href="<?php echo get_post_type_archive_link('novel'); ?>" class="browse-novels-btn">
                            <?php _e('Browse All Novels', 'epic-novel-theme'); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </section>

        <?php endif; ?>

        <!-- Ad Space - Footer -->
        <div class="ad-space footer-ad">
            <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
        </div>

    </main>
</div>

<script>
jQuery(document).ready(function($) {
    let currentPage = 1;
    let loading = false;

    // Load more novels
    $('#load-more-novels').on('click', function() {
        if (loading) return;

        loading = true;
        const button = $(this);
        const loadMoreText = button.find('.load-more-text');
        const loadMoreSpinner = button.find('.load-more-spinner');

        loadMoreText.hide();
        loadMoreSpinner.show();
        button.prop('disabled', true);

        currentPage++;

        $.ajax({
            url: epic_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'epic_load_more_homepage_novels',
                nonce: epic_ajax.nonce,
                page: currentPage
            },
            success: function(response) {
                if (response.success) {
                    const novels = response.data.novels;

                    novels.forEach(function(novel) {
                        let chaptersHtml = '';
                        novel.recent_chapters.forEach(function(chapter) {
                            const bookmarkClass = chapter.is_bookmarked ? 'bookmarked' : '';
                            const bookmarkButton = epic_ajax.user_id ?
                                `<button class="bookmark-btn ${bookmarkClass}" data-post-id="${chapter.id}" aria-label="Bookmark chapter">
                                    <i class="fas fa-bookmark"></i>
                                </button>` : '';

                            chaptersHtml += `
                                <div class="chapter-item">
                                    <div class="chapter-info">
                                        <h5 class="chapter-title">
                                            <a href="${chapter.url}">
                                                ${chapter.chapter_number ? `Chapter ${chapter.chapter_number}: ` : ''}${chapter.title}
                                            </a>
                                        </h5>
                                        ${chapter.volume_title ? `<p class="volume-title">${chapter.volume_title}</p>` : ''}
                                        <div class="chapter-meta">
                                            <span class="chapter-date">
                                                <i class="fas fa-calendar"></i>
                                                ${chapter.date}
                                            </span>
                                            <span class="reading-time">
                                                <i class="fas fa-clock"></i>
                                                ${chapter.reading_time} min
                                            </span>
                                        </div>
                                    </div>
                                    <div class="chapter-actions">
                                        <a href="${chapter.url}" class="read-btn">
                                            <i class="fas fa-book-open"></i>
                                            Read
                                        </a>
                                        ${bookmarkButton}
                                    </div>
                                </div>
                            `;
                        });

                        const novelBookmarkClass = novel.is_bookmarked ? 'bookmarked' : '';
                        const novelBookmarkButton = epic_ajax.user_id ?
                            `<button class="bookmark-btn ${novelBookmarkClass}" data-post-id="${novel.id}" aria-label="Bookmark this novel">
                                <i class="fas fa-bookmark"></i>
                            </button>` : '';

                        const statusBadge = novel.status ?
                            `<div class="novel-status">
                                <span class="status-badge status-${novel.status}">${novel.status}</span>
                            </div>` : '';

                        const ratingHtml = novel.rating > 0 ?
                            `<div class="novel-rating">
                                <div class="stars">
                                    ${generateStars(novel.rating)}
                                </div>
                                <span class="rating-text">${parseFloat(novel.rating).toFixed(1)}</span>
                            </div>` : '';

                        const writersHtml = novel.writers.length > 0 ?
                            `<div class="novel-writers">
                                <i class="fas fa-user"></i>
                                ${novel.writers.join(', ')}
                            </div>` : '';

                        const latestChapterBtn = novel.recent_chapters.length > 0 ?
                            `<a href="${novel.recent_chapters[0].url}" class="latest-chapter-btn">
                                <i class="fas fa-forward"></i>
                                Latest Chapter
                            </a>` : '';

                        const novelHtml = `
                            <article id="novel-${novel.id}" class="novel-homepage-card">
                                <div class="novel-card-cover">
                                    <a href="${novel.url}">
                                        ${novel.thumbnail ?
                                            `<img src="${novel.thumbnail}" alt="${novel.title}">` :
                                            `<div class="novel-placeholder"><i class="fas fa-book"></i></div>`
                                        }
                                    </a>
                                    ${novelBookmarkButton}
                                    <div class="update-badge">
                                        <i class="fas fa-clock"></i>
                                        <span>${novel.time_ago} ago</span>
                                    </div>
                                </div>
                                <div class="novel-card-content">
                                    <h2 class="novel-title">
                                        <a href="${novel.url}">${novel.title}</a>
                                    </h2>
                                    ${novel.alternative_title ? `<p class="alternative-title">${novel.alternative_title}</p>` : ''}
                                    <div class="novel-meta">
                                        ${writersHtml}
                                        ${statusBadge}
                                        ${ratingHtml}
                                    </div>
                                    <div class="novel-description">
                                        ${novel.summary ? novel.summary.substring(0, 150) + '...' : ''}
                                    </div>
                                    <div class="recent-chapters">
                                        <h4 class="recent-chapters-title">Latest Chapters</h4>
                                        <div class="chapters-list">
                                            ${chaptersHtml}
                                        </div>
                                    </div>
                                    <div class="novel-actions">
                                        <a href="${novel.url}" class="view-novel-btn">
                                            <i class="fas fa-info-circle"></i>
                                            Novel Info
                                        </a>
                                        ${latestChapterBtn}
                                    </div>
                                </div>
                            </article>
                        `;

                        $('#novels-homepage-grid').append(novelHtml);
                    });

                    if (!response.data.has_more) {
                        $('#load-more-container').hide();
                    }
                } else {
                    alert('Failed to load more novels.');
                }
            },
            error: function() {
                alert('Failed to load more novels.');
            },
            complete: function() {
                loading = false;
                loadMoreSpinner.hide();
                loadMoreText.show();
                button.prop('disabled', false);
            }
        });
    });

    // Helper function to generate star ratings
    function generateStars(rating) {
        const fullStars = Math.floor(rating / 2);
        const halfStar = (rating / 2) - fullStars >= 0.5;
        let starsHtml = '';

        for (let i = 1; i <= 5; i++) {
            if (i <= fullStars) {
                starsHtml += '<i class="fas fa-star"></i>';
            } else if (i === fullStars + 1 && halfStar) {
                starsHtml += '<i class="fas fa-star-half-alt"></i>';
            } else {
                starsHtml += '<i class="far fa-star"></i>';
            }
        }

        return starsHtml;
    }
});
</script>

<?php
get_sidebar();
get_footer();
?>
