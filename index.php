<?php
/**
 * The main template file
 * 
 * @package EpicNovelTheme
 */

get_header(); ?>

<div class="container">
    <main id="main" class="site-main">
        
        <?php if (have_posts()) : ?>
            
            <header class="page-header">
                <?php if (is_home() && !is_front_page()) : ?>
                    <h1 class="page-title"><?php single_post_title(); ?></h1>
                <?php elseif (is_archive()) : ?>
                    <h1 class="page-title">
                        <?php
                        if (is_category()) {
                            single_cat_title();
                        } elseif (is_tag()) {
                            single_tag_title();
                        } elseif (is_author()) {
                            printf(__('Author: %s', 'epic-novel-theme'), '<span class="vcard">' . get_the_author() . '</span>');
                        } elseif (is_tax()) {
                            single_term_title();
                        } else {
                            the_archive_title();
                        }
                        ?>
                    </h1>
                    <?php the_archive_description('<div class="archive-description">', '</div>'); ?>
                <?php else : ?>
                    <h1 class="page-title"><?php _e('Latest Chapters', 'epic-novel-theme'); ?></h1>
                <?php endif; ?>
            </header>

            <!-- Ad Space - Header -->
            <div class="ad-space header-ad">
                <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
            </div>

            <div class="posts-container">
                <?php while (have_posts()) : the_post(); ?>
                    
                    <article id="post-<?php the_ID(); ?>" <?php post_class('chapter-card'); ?>>
                        
                        <header class="entry-header">
                            <h2 class="entry-title">
                                <a href="<?php the_permalink(); ?>" rel="bookmark">
                                    <?php
                                    // Display chapter information
                                    $novel_title = get_post_meta(get_the_ID(), 'novel_title', true);
                                    $chapter_number = get_post_meta(get_the_ID(), 'chapter_number', true);
                                    $volume_title = get_post_meta(get_the_ID(), 'volume_title', true);
                                    
                                    if ($novel_title) {
                                        echo esc_html($novel_title);
                                        if ($volume_title) {
                                            echo ' - ' . esc_html($volume_title);
                                        }
                                        if ($chapter_number) {
                                            echo ' - Chapter ' . esc_html($chapter_number);
                                        }
                                        if (get_the_title()) {
                                            echo ': ' . get_the_title();
                                        }
                                    } else {
                                        the_title();
                                    }
                                    ?>
                                </a>
                            </h2>
                            
                            <div class="entry-meta">
                                <span class="posted-on">
                                    <i class="fas fa-calendar"></i>
                                    <time datetime="<?php echo get_the_date('c'); ?>">
                                        <?php echo get_the_date(); ?>
                                    </time>
                                </span>
                                
                                <?php if ($novel_title) : ?>
                                    <span class="novel-link">
                                        <i class="fas fa-book"></i>
                                        <a href="<?php echo esc_url(get_post_type_archive_link('novel')); ?>">
                                            <?php echo esc_html($novel_title); ?>
                                        </a>
                                    </span>
                                <?php endif; ?>
                                
                                <span class="reading-time">
                                    <i class="fas fa-clock"></i>
                                    <?php echo epic_get_reading_time(get_the_ID()); ?> min read
                                </span>
                            </div>
                        </header>

                        <div class="entry-content">
                            <?php
                            if (has_excerpt()) {
                                the_excerpt();
                            } else {
                                echo wp_trim_words(get_the_content(), 30, '...');
                            }
                            ?>
                        </div>

                        <footer class="entry-footer">
                            <div class="chapter-actions">
                                <a href="<?php the_permalink(); ?>" class="read-chapter-btn">
                                    <i class="fas fa-book-open"></i>
                                    <?php _e('Read Chapter', 'epic-novel-theme'); ?>
                                </a>
                                
                                <?php if (is_user_logged_in()) : ?>
                                    <button class="bookmark-btn" data-post-id="<?php the_ID(); ?>">
                                        <i class="fas fa-bookmark"></i>
                                        <?php _e('Bookmark', 'epic-novel-theme'); ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </footer>
                        
                    </article>

                <?php endwhile; ?>
            </div>

            <!-- Pagination -->
            <nav class="pagination-nav">
                <?php
                the_posts_pagination(array(
                    'mid_size' => 2,
                    'prev_text' => '<i class="fas fa-chevron-left"></i> ' . __('Previous', 'epic-novel-theme'),
                    'next_text' => __('Next', 'epic-novel-theme') . ' <i class="fas fa-chevron-right"></i>',
                ));
                ?>
            </nav>

        <?php else : ?>
            
            <section class="no-results not-found">
                <header class="page-header">
                    <h1 class="page-title"><?php _e('Nothing here', 'epic-novel-theme'); ?></h1>
                </header>

                <div class="page-content">
                    <?php if (is_home() && current_user_can('publish_posts')) : ?>
                        <p><?php
                        printf(
                            wp_kses(
                                __('Ready to publish your first chapter? <a href="%1$s">Get started here</a>.', 'epic-novel-theme'),
                                array(
                                    'a' => array(
                                        'href' => array(),
                                    ),
                                )
                            ),
                            esc_url(admin_url('post-new.php'))
                        );
                        ?></p>
                    <?php elseif (is_search()) : ?>
                        <p><?php _e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'epic-novel-theme'); ?></p>
                        <?php get_search_form(); ?>
                    <?php else : ?>
                        <p><?php _e('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'epic-novel-theme'); ?></p>
                        <?php get_search_form(); ?>
                    <?php endif; ?>
                </div>
            </section>

        <?php endif; ?>

        <!-- Ad Space - Footer -->
        <div class="ad-space footer-ad">
            <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
        </div>

    </main>
</div>

<?php
get_sidebar();
get_footer();
?>
