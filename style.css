/*
Theme Name: Epic Novel Theme
Description: A comprehensive WordPress theme designed specifically for reading light novels and web novels with advanced features including night mode, bookmark system, reading history, and Google OAuth integration.
Version: 1.0.0
Author: Epic Theme Developer
Text Domain: epic-novel-theme
Domain Path: /languages
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Tags: novels, reading, night-mode, responsive, seo-friendly
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Georgia', 'Times New Roman', serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Night Mode Styles */
body.night-mode {
    background-color: #1a1a1a;
    color: #e0e0e0;
}

body.night-mode .header,
body.night-mode .footer {
    background-color: #2d2d2d;
    border-color: #444;
}

body.night-mode .novel-card,
body.night-mode .chapter-content {
    background-color: #2d2d2d;
    border-color: #444;
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.reading-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.site-header {
    background-color: #fff;
    border-bottom: 1px solid #eee;
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: background-color 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-title {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    text-decoration: none;
}

.night-mode .site-title {
    color: #e0e0e0;
}

/* Navigation Styles */
.main-navigation ul {
    list-style: none;
    display: flex;
    gap: 2rem;
}

.main-navigation a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.main-navigation a:hover {
    color: #007cba;
}

.night-mode .main-navigation a {
    color: #e0e0e0;
}

.night-mode .main-navigation a:hover {
    color: #4a9eff;
}

/* User Controls */
.user-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.night-mode-toggle {
    background: none;
    border: 1px solid #ccc;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.night-mode-toggle:hover {
    background-color: #f5f5f5;
}

.night-mode .night-mode-toggle {
    border-color: #666;
    color: #e0e0e0;
}

.night-mode .night-mode-toggle:hover {
    background-color: #3a3a3a;
}

/* Reading Controls */
.reading-controls {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 999;
}

.night-mode .reading-controls {
    background: rgba(45, 45, 45, 0.9);
    border-color: #666;
}

.font-size-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.font-size-btn {
    background: #007cba;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.font-size-btn:hover {
    background: #005a87;
}

/* Novel Cards */
.novels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.novel-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.novel-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.novel-title {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.novel-meta {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.night-mode .novel-meta {
    color: #aaa;
}

.novel-description {
    margin-bottom: 1rem;
    line-height: 1.5;
}

.novel-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tag {
    background: #f0f0f0;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #666;
}

.night-mode .tag {
    background: #444;
    color: #ccc;
}

/* Chapter Content */
.chapter-content {
    background: #fff;
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
    line-height: 1.8;
    font-size: 1.1rem;
}

.chapter-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    text-align: center;
}

.chapter-meta {
    text-align: center;
    color: #666;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.night-mode .chapter-meta {
    color: #aaa;
    border-color: #444;
}

/* Chapter Navigation */
.chapter-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2rem 0;
    padding: 1rem;
    background: #f9f9f9;
    border-radius: 8px;
}

.night-mode .chapter-navigation {
    background: #2d2d2d;
}

.nav-btn {
    background: #007cba;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.nav-btn:hover {
    background: #005a87;
}

.nav-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Progress Bar */
.reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: #007cba;
    z-index: 1001;
    transition: width 0.1s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-navigation ul {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .novels-grid {
        grid-template-columns: 1fr;
    }
    
    .reading-controls {
        position: static;
        transform: none;
        margin: 1rem 0;
        flex-direction: row;
        justify-content: center;
    }
    
    .chapter-navigation {
        flex-direction: column;
        gap: 1rem;
    }
    
    .container,
    .reading-container {
        padding: 0 15px;
    }
}

/* AdSense Friendly Ad Spaces */
.ad-space {
    margin: 2rem 0;
    text-align: center;
    min-height: 250px;
    background: #f9f9f9;
    border: 1px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

.night-mode .ad-space {
    background: #2d2d2d;
    border-color: #666;
}

.ad-space.header-ad {
    margin: 1rem 0;
    min-height: 90px;
}

.ad-space.sidebar-ad {
    min-height: 600px;
}

.ad-space.footer-ad {
    margin: 1rem 0;
    min-height: 250px;
}

/* Utility Classes */
.text-center { text-align: center; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Search and Filter Styles */
.search-filters {
    background: #f9f9f9;
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
}

.night-mode .search-filters {
    background: #2d2d2d;
}

.filter-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.night-mode .filter-group select,
.night-mode .filter-group input {
    background: #3a3a3a;
    border-color: #666;
    color: #e0e0e0;
}

/* Bookmark and User Features */
.bookmark-btn {
    background: none;
    border: 1px solid #007cba;
    color: #007cba;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bookmark-btn:hover,
.bookmark-btn.bookmarked {
    background: #007cba;
    color: white;
}

.user-dashboard {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    margin: 2rem 0;
}

.dashboard-sidebar {
    background: #f9f9f9;
    padding: 1.5rem;
    border-radius: 8px;
}

.night-mode .dashboard-sidebar {
    background: #2d2d2d;
}

.dashboard-content {
    background: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #ddd;
}

.night-mode .dashboard-content {
    background: #2d2d2d;
    border-color: #666;
}

@media (max-width: 768px) {
    .user-dashboard {
        grid-template-columns: 1fr;
    }

    .filter-row {
        flex-direction: column;
    }
}

/* Novel Single Page Styles */
.novel-single .novel-header {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.novel-cover-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.novel-cover-placeholder {
    width: 200px;
    height: 280px;
    background: #f0f0f0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #ccc;
}

.night-mode .novel-cover-placeholder {
    background: #3a3a3a;
    color: #666;
}

.novel-info h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.alternative-title {
    font-style: italic;
    color: #666;
    margin-bottom: 1rem;
}

.night-mode .alternative-title {
    color: #aaa;
}

.novel-meta {
    margin-bottom: 1.5rem;
}

.novel-meta > div {
    margin-bottom: 0.5rem;
}

.meta-label {
    font-weight: bold;
    margin-right: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-ongoing {
    background: #e8f5e8;
    color: #2d5a2d;
}

.status-completed {
    background: #e8f0ff;
    color: #1e3a8a;
}

.status-hiatus {
    background: #fff3cd;
    color: #856404;
}

.status-dropped {
    background: #f8d7da;
    color: #721c24;
}

.night-mode .status-ongoing {
    background: #2d5a2d;
    color: #90ee90;
}

.night-mode .status-completed {
    background: #1e3a8a;
    color: #87ceeb;
}

.night-mode .status-hiatus {
    background: #856404;
    color: #ffd700;
}

.night-mode .status-dropped {
    background: #721c24;
    color: #ffb6c1;
}

.novel-rating .stars {
    display: inline-block;
    margin-right: 0.5rem;
}

.novel-rating .stars i {
    color: #ffb900;
    margin-right: 2px;
}

.novel-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.novel-actions button,
.novel-actions a {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.bookmark-btn {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.bookmark-btn:hover,
.bookmark-btn.bookmarked {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

.share-btn {
    background: #28a745;
    color: white;
}

.share-btn:hover {
    background: #218838;
}

.external-link-btn {
    background: #6c757d;
    color: white;
}

.external-link-btn:hover {
    background: #5a6268;
}

/* Novel Content Layout */
.novel-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    margin-top: 2rem;
}

.novel-description h2 {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #007cba;
}

.novel-taxonomies {
    margin: 2rem 0;
}

.novel-taxonomies h3 {
    margin-bottom: 1rem;
}

.taxonomy-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.taxonomy-tag {
    background: #f8f9fa;
    color: #333;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.taxonomy-tag:hover {
    background: #007cba;
    color: white;
}

.night-mode .taxonomy-tag {
    background: #3a3a3a;
    color: #e0e0e0;
}

.night-mode .taxonomy-tag:hover {
    background: #4a9eff;
}

/* Chapter List Styles */
.novel-chapters {
    margin: 2rem 0;
}

.chapters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.chapters-controls select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.night-mode .chapters-controls select {
    background: #3a3a3a;
    border-color: #666;
    color: #e0e0e0;
}

.chapter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.chapter-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.night-mode .chapter-item {
    border-color: #444;
    background: #2d2d2d;
}

.night-mode .chapter-item:hover {
    background: #3a3a3a;
}

.chapter-info h4 {
    margin-bottom: 0.25rem;
}

.chapter-info h4 a {
    text-decoration: none;
    color: #333;
}

.chapter-info h4 a:hover {
    color: #007cba;
}

.night-mode .chapter-info h4 a {
    color: #e0e0e0;
}

.night-mode .chapter-info h4 a:hover {
    color: #4a9eff;
}

.chapter-meta {
    font-size: 0.9rem;
    color: #666;
}

.night-mode .chapter-meta {
    color: #aaa;
}

.chapter-actions {
    display: flex;
    gap: 0.5rem;
}

.chapter-actions .read-btn {
    background: #007cba;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.9rem;
}

.chapter-actions .read-btn:hover {
    background: #005a87;
}

/* Sidebar Widgets */
.novel-sidebar .widget {
    background: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.night-mode .novel-sidebar .widget {
    background: #2d2d2d;
    border-color: #444;
}

.novel-sidebar h3 {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ddd;
}

.night-mode .novel-sidebar h3 {
    border-color: #555;
}

.stats-list .stat-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.stats-list .stat-item:last-child {
    border-bottom: none;
}

.night-mode .stats-list .stat-item {
    border-color: #444;
}

.stat-label {
    font-weight: 500;
}

.stat-value {
    color: #007cba;
    font-weight: bold;
}

.night-mode .stat-value {
    color: #4a9eff;
}

/* Related Novels */
.related-novel-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.related-novel-item:last-child {
    border-bottom: none;
}

.night-mode .related-novel-item {
    border-color: #444;
}

.related-novel-item img {
    width: 60px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
}

.related-novel-info h4 {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.related-novel-info p {
    font-size: 0.8rem;
    color: #666;
    line-height: 1.4;
}

.night-mode .related-novel-info p {
    color: #aaa;
}

/* Chapter Single Page Styles */
.chapter-single .chapter-header {
    text-align: center;
    margin-bottom: 2rem;
}

.novel-breadcrumb {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    color: #666;
}

.novel-breadcrumb a {
    color: #007cba;
    text-decoration: none;
}

.novel-breadcrumb a:hover {
    text-decoration: underline;
}

.novel-breadcrumb .separator {
    margin: 0 0.5rem;
}

.night-mode .novel-breadcrumb {
    color: #aaa;
}

.night-mode .novel-breadcrumb a {
    color: #4a9eff;
}

.chapter-title {
    font-size: 2rem;
    line-height: 1.3;
    margin-bottom: 1rem;
}

.chapter-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.night-mode .chapter-meta {
    border-color: #444;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.night-mode .meta-item {
    color: #aaa;
}

.meta-item i {
    color: #007cba;
}

.night-mode .meta-item i {
    color: #4a9eff;
}

/* Chapter Navigation */
.chapter-navigation {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 1rem;
    align-items: center;
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #eee;
}

.night-mode .chapter-navigation {
    background: #2d2d2d;
    border-color: #444;
}

.nav-previous {
    justify-self: start;
}

.nav-center {
    justify-self: center;
}

.nav-next {
    justify-self: end;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
    max-width: 250px;
}

.nav-btn:hover {
    background: #005a87;
    transform: translateY(-2px);
}

.nav-btn.disabled {
    background: #ccc;
    color: #999;
    cursor: not-allowed;
    transform: none;
}

.nav-btn.disabled:hover {
    background: #ccc;
    transform: none;
}

.night-mode .nav-btn.disabled {
    background: #555;
    color: #777;
}

.nav-text {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.nav-label {
    font-size: 0.8rem;
    opacity: 0.8;
}

.nav-title {
    font-size: 0.9rem;
    font-weight: 500;
}

.novel-link-btn {
    background: #28a745;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.novel-link-btn:hover {
    background: #218838;
}

/* Chapter Content */
.chapter-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    line-height: 1.8;
    font-size: 1.1rem;
}

.night-mode .chapter-content {
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.chapter-content p {
    margin-bottom: 1.5rem;
}

.chapter-content h1,
.chapter-content h2,
.chapter-content h3,
.chapter-content h4,
.chapter-content h5,
.chapter-content h6 {
    margin: 2rem 0 1rem 0;
    line-height: 1.3;
}

.translator-notes {
    margin-top: 3rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-left: 4px solid #007cba;
    border-radius: 0 4px 4px 0;
}

.night-mode .translator-notes {
    background: #3a3a3a;
    border-color: #4a9eff;
}

.translator-notes h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #007cba;
}

.night-mode .translator-notes h3 {
    color: #4a9eff;
}

/* Chapter Footer */
.chapter-footer {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.night-mode .chapter-footer {
    border-color: #444;
}

.chapter-tags {
    margin-bottom: 2rem;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.tags-label {
    font-weight: 500;
    margin-right: 0.5rem;
}

.tag-link {
    background: #f0f0f0;
    color: #333;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.tag-link:hover {
    background: #007cba;
    color: white;
}

.night-mode .tag-link {
    background: #3a3a3a;
    color: #e0e0e0;
}

.night-mode .tag-link:hover {
    background: #4a9eff;
}

.chapter-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.social-share {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.share-label {
    font-weight: 500;
}

.social-share a,
.social-share button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    text-decoration: none;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.share-twitter {
    background: #1da1f2;
}

.share-facebook {
    background: #4267b2;
}

.share-reddit {
    background: #ff4500;
}

.copy-link {
    background: #6c757d;
}

.social-share a:hover,
.social-share button:hover {
    transform: scale(1.1);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: #28a745;
}

.notification-error {
    background: #dc3545;
}

.notification-warning {
    background: #ffc107;
    color: #333;
}

.notification-info {
    background: #17a2b8;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    margin-left: auto;
}

/* Rating Modal */
.rating-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rating-modal-content {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.night-mode .rating-modal-content {
    background: #2d2d2d;
}

.rating-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.close-rating-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.night-mode .close-rating-modal {
    color: #aaa;
}

.rating-stars {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 1.5rem 0;
}

.rating-star {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #ddd;
    transition: color 0.2s ease;
}

.rating-star:hover,
.rating-star.active {
    color: #ffb900;
}

.rating-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 1rem 0;
    color: #007cba;
}

.night-mode .rating-value {
    color: #4a9eff;
}

.rating-modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

.submit-rating,
.cancel-rating {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.submit-rating {
    background: #007cba;
    color: white;
}

.submit-rating:hover:not(:disabled) {
    background: #005a87;
}

.submit-rating:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.cancel-rating {
    background: #6c757d;
    color: white;
}

.cancel-rating:hover {
    background: #5a6268;
}

/* Enhanced Mobile Responsive Styles */
@media (max-width: 1024px) {
    .novel-content {
        grid-template-columns: 1fr;
    }

    .novel-sidebar {
        order: -1;
    }

    .novels-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    /* Header adjustments */
    .site-header {
        padding: 0.5rem 0;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .site-title {
        font-size: 1.5rem;
    }

    .main-navigation ul {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .user-controls {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    /* Novel header mobile */
    .novel-single .novel-header {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1rem;
    }

    .novel-cover {
        justify-self: center;
    }

    .novel-cover-image,
    .novel-cover-placeholder {
        width: 150px;
        height: 210px;
    }

    .novel-info h1 {
        font-size: 1.8rem;
    }

    .novel-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    /* Chapter navigation mobile */
    .chapter-navigation {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }

    .nav-previous,
    .nav-center,
    .nav-next {
        justify-self: center;
    }

    .nav-btn {
        max-width: 100%;
        justify-content: center;
    }

    /* Chapter meta mobile */
    .chapter-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .chapter-title {
        font-size: 1.5rem;
    }

    /* Chapter content mobile */
    .chapter-content {
        padding: 1rem;
        font-size: 1rem;
    }

    /* Chapter footer mobile */
    .chapter-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .social-share {
        justify-content: center;
    }

    /* Novel cards mobile */
    .novels-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .novel-card {
        padding: 1rem;
    }

    /* Chapter items mobile */
    .chapter-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .chapter-actions {
        align-self: stretch;
        justify-content: center;
    }

    /* Search filters mobile */
    .search-filters {
        padding: 1rem;
    }

    .filter-row {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-group {
        min-width: auto;
    }

    /* Dashboard mobile */
    .user-dashboard {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .dashboard-sidebar {
        order: 1;
    }

    .dashboard-content {
        order: 2;
    }

    /* Stats grid mobile */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    /* Reading controls mobile */
    .reading-controls {
        position: static;
        transform: none;
        margin: 1rem 0;
        flex-direction: row;
        justify-content: center;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .night-mode .reading-controls {
        background: rgba(45, 45, 45, 0.95);
    }

    /* Mobile reading controls */
    .mobile-reading-controls {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-top: 1px solid #ddd;
        padding: 1rem;
        z-index: 999;
    }

    .night-mode .mobile-reading-controls {
        background: rgba(45, 45, 45, 0.95);
        border-color: #666;
    }

    .mobile-controls-content {
        display: flex;
        justify-content: space-around;
        align-items: center;
        max-width: 400px;
        margin: 0 auto;
    }

    .font-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .mobile-night-toggle,
    .mobile-bookmark {
        background: none;
        border: 1px solid #ddd;
        padding: 0.5rem;
        border-radius: 4px;
        cursor: pointer;
        color: #333;
    }

    .night-mode .mobile-night-toggle,
    .night-mode .mobile-bookmark {
        border-color: #666;
        color: #e0e0e0;
    }

    /* Notification mobile */
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }

    /* Rating modal mobile */
    .rating-modal-content {
        margin: 1rem;
        width: calc(100% - 2rem);
    }

    .rating-stars {
        gap: 0.25rem;
    }

    .rating-star {
        font-size: 1.5rem;
    }

    .rating-modal-footer {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    /* Extra small mobile adjustments */
    .container,
    .reading-container {
        padding: 0 10px;
    }

    .novel-info h1 {
        font-size: 1.5rem;
    }

    .chapter-title {
        font-size: 1.3rem;
    }

    .chapter-content {
        padding: 0.75rem;
        font-size: 0.95rem;
    }

    .novel-card {
        padding: 0.75rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .nav-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .nav-text {
        gap: 0.125rem;
    }

    .nav-label {
        font-size: 0.7rem;
    }

    .nav-title {
        font-size: 0.8rem;
    }
}

/* Print Styles */
@media print {
    .site-header,
    .site-footer,
    .chapter-navigation,
    .reading-controls,
    .mobile-reading-controls,
    .ad-space,
    .social-share,
    .bookmark-btn,
    .share-btn {
        display: none !important;
    }

    .chapter-content {
        box-shadow: none;
        background: white;
        color: black;
        font-size: 12pt;
        line-height: 1.6;
    }

    .chapter-title {
        font-size: 18pt;
        margin-bottom: 1rem;
    }

    .chapter-meta {
        font-size: 10pt;
        margin-bottom: 1rem;
    }

    .translator-notes {
        background: #f5f5f5;
        border: 1px solid #ddd;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .novel-card,
    .chapter-content,
    .chapter-navigation {
        border: 2px solid #000;
    }

    .nav-btn {
        border: 2px solid #000;
    }

    .bookmark-btn,
    .share-btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .reading-progress {
        transition: none;
    }
}
