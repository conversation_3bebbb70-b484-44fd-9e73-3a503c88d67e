/**
 * Epic Novel Theme Main JavaScript
 * 
 * @package EpicNovelTheme
 */

(function($) {
    'use strict';
    
    // Theme object
    const EpicTheme = {
        
        // Initialize theme
        init: function() {
            this.setupNightMode();
            this.setupReadingControls();
            this.setupBookmarks();
            this.setupSearch();
            this.setupMobileMenu();
            this.setupReadingProgress();
            this.setupLazyLoading();
            this.setupInfiniteScroll();
            this.setupTooltips();
        },
        
        // Night mode functionality
        setupNightMode: function() {
            const nightModeToggle = $('.night-mode-toggle');
            const body = $('body');
            
            // Check for saved preference
            const savedNightMode = localStorage.getItem('epic-night-mode');
            if (savedNightMode === 'enabled') {
                body.addClass('night-mode');
                nightModeToggle.find('i').removeClass('fa-moon').addClass('fa-sun');
            }
            
            // Toggle night mode
            nightModeToggle.on('click', function() {
                body.toggleClass('night-mode');
                
                const isNightMode = body.hasClass('night-mode');
                const icon = $(this).find('i');
                
                if (isNightMode) {
                    localStorage.setItem('epic-night-mode', 'enabled');
                    icon.removeClass('fa-moon').addClass('fa-sun');
                } else {
                    localStorage.setItem('epic-night-mode', 'disabled');
                    icon.removeClass('fa-sun').addClass('fa-moon');
                }
            });
        },
        
        // Reading controls (font size, reading mode)
        setupReadingControls: function() {
            const increaseFontBtn = $('#increase-font, #mobile-increase-font');
            const decreaseFontBtn = $('#decrease-font, #mobile-decrease-font');
            const fontSizeDisplay = $('#font-size-display, #mobile-font-size-display');
            const readingModeBtn = $('#reading-mode-btn');
            
            // Get saved font size
            let currentFontSize = parseInt(localStorage.getItem('epic-font-size')) || 100;
            this.updateFontSize(currentFontSize);
            fontSizeDisplay.text(currentFontSize + '%');
            
            // Increase font size
            increaseFontBtn.on('click', function() {
                if (currentFontSize < 200) {
                    currentFontSize += 10;
                    EpicTheme.updateFontSize(currentFontSize);
                    fontSizeDisplay.text(currentFontSize + '%');
                    localStorage.setItem('epic-font-size', currentFontSize);
                }
            });
            
            // Decrease font size
            decreaseFontBtn.on('click', function() {
                if (currentFontSize > 60) {
                    currentFontSize -= 10;
                    EpicTheme.updateFontSize(currentFontSize);
                    fontSizeDisplay.text(currentFontSize + '%');
                    localStorage.setItem('epic-font-size', currentFontSize);
                }
            });
            
            // Reading mode toggle
            readingModeBtn.on('click', function() {
                $('body').toggleClass('reading-mode');
                $(this).toggleClass('active');
                
                const isReadingMode = $('body').hasClass('reading-mode');
                localStorage.setItem('epic-reading-mode', isReadingMode ? 'enabled' : 'disabled');
            });
            
            // Restore reading mode
            if (localStorage.getItem('epic-reading-mode') === 'enabled') {
                $('body').addClass('reading-mode');
                readingModeBtn.addClass('active');
            }
        },
        
        // Update font size
        updateFontSize: function(size) {
            const percentage = size / 100;
            $('.chapter-content, .novel-description').css('font-size', percentage + 'rem');
        },
        
        // Bookmark functionality
        setupBookmarks: function() {
            $(document).on('click', '.bookmark-btn', function(e) {
                e.preventDefault();
                
                if (!epic_ajax.user_id) {
                    alert('Please log in to bookmark content.');
                    return;
                }
                
                const button = $(this);
                const postId = button.data('post-id');
                const icon = button.find('i');
                
                button.prop('disabled', true);
                
                $.ajax({
                    url: epic_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'epic_toggle_bookmark',
                        nonce: epic_ajax.nonce,
                        post_id: postId
                    },
                    success: function(response) {
                        if (response.success) {
                            const isBookmarked = response.data.is_bookmarked;
                            
                            if (isBookmarked) {
                                button.addClass('bookmarked');
                                icon.removeClass('far').addClass('fas');
                            } else {
                                button.removeClass('bookmarked');
                                icon.removeClass('fas').addClass('far');
                            }
                            
                            // Show notification
                            EpicTheme.showNotification(response.data.message, 'success');
                        } else {
                            EpicTheme.showNotification(response.data, 'error');
                        }
                    },
                    error: function() {
                        EpicTheme.showNotification('Failed to update bookmark.', 'error');
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            });
        },
        
        // Search functionality
        setupSearch: function() {
            const searchToggle = $('.search-toggle');
            const searchContainer = $('.search-form-container');
            const mobileSearchOverlay = $('#mobile-search-overlay');
            const closeSearch = $('.close-search');
            
            // Toggle search
            searchToggle.on('click', function() {
                if ($(window).width() <= 768) {
                    mobileSearchOverlay.fadeIn(300);
                    mobileSearchOverlay.find('input[type="search"]').focus();
                } else {
                    searchContainer.slideToggle(300);
                    searchContainer.find('input[type="search"]').focus();
                }
            });
            
            // Close mobile search
            closeSearch.on('click', function() {
                mobileSearchOverlay.fadeOut(300);
            });
            
            // Close search on overlay click
            mobileSearchOverlay.on('click', function(e) {
                if (e.target === this) {
                    $(this).fadeOut(300);
                }
            });
            
            // Live search for novels
            let searchTimeout;
            $('.novel-search-input').on('input', function() {
                const searchTerm = $(this).val();
                const resultsContainer = $('.search-results');
                
                clearTimeout(searchTimeout);
                
                if (searchTerm.length < 2) {
                    resultsContainer.hide();
                    return;
                }
                
                searchTimeout = setTimeout(function() {
                    $.ajax({
                        url: epic_ajax.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'epic_search_novels',
                            nonce: epic_ajax.nonce,
                            search_term: searchTerm,
                            limit: 5
                        },
                        success: function(response) {
                            if (response.success) {
                                const novels = response.data.novels;
                                let html = '';
                                
                                novels.forEach(function(novel) {
                                    html += `
                                        <div class="search-result-item">
                                            <a href="${novel.url}">
                                                ${novel.thumbnail ? `<img src="${novel.thumbnail}" alt="${novel.title}">` : '<div class="no-image"><i class="fas fa-book"></i></div>'}
                                                <div class="result-info">
                                                    <h4>${novel.title}</h4>
                                                    ${novel.alternative_title ? `<p class="alt-title">${novel.alternative_title}</p>` : ''}
                                                </div>
                                            </a>
                                        </div>
                                    `;
                                });
                                
                                if (html) {
                                    resultsContainer.html(html).show();
                                } else {
                                    resultsContainer.html('<p class="no-results">No novels found.</p>').show();
                                }
                            }
                        }
                    });
                }, 300);
            });
            
            // Hide search results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.search-form-container').length) {
                    $('.search-results').hide();
                }
            });
        },
        
        // Mobile menu
        setupMobileMenu: function() {
            const menuToggle = $('.menu-toggle');
            const navigation = $('.main-navigation');
            
            menuToggle.on('click', function() {
                $(this).toggleClass('active');
                navigation.toggleClass('active');
                $('body').toggleClass('menu-open');
            });
            
            // Close menu when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.main-navigation, .menu-toggle').length) {
                    menuToggle.removeClass('active');
                    navigation.removeClass('active');
                    $('body').removeClass('menu-open');
                }
            });
        },
        
        // Reading progress bar
        setupReadingProgress: function() {
            const progressBar = $('#reading-progress');
            
            if (progressBar.length) {
                $(window).on('scroll', function() {
                    const winScroll = $(window).scrollTop();
                    const height = $(document).height() - $(window).height();
                    const scrolled = (winScroll / height) * 100;
                    
                    progressBar.css('width', Math.min(100, Math.max(0, scrolled)) + '%');
                });
            }
        },
        
        // Lazy loading for images
        setupLazyLoading: function() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });
                
                $('.lazy').each(function() {
                    imageObserver.observe(this);
                });
            }
        },
        
        // Infinite scroll for novel archives
        setupInfiniteScroll: function() {
            if ($('.novels-grid').length && $('.load-more-novels').length) {
                let loading = false;
                
                $(window).on('scroll', function() {
                    if (loading) return;
                    
                    const scrollTop = $(window).scrollTop();
                    const windowHeight = $(window).height();
                    const documentHeight = $(document).height();
                    
                    // Trigger when 200px from bottom
                    if (scrollTop + windowHeight >= documentHeight - 200) {
                        $('.load-more-novels').trigger('click');
                        loading = true;
                        
                        setTimeout(function() {
                            loading = false;
                        }, 2000);
                    }
                });
            }
        },
        
        // Tooltips
        setupTooltips: function() {
            $('[data-tooltip]').each(function() {
                const element = $(this);
                const tooltipText = element.data('tooltip');
                
                element.on('mouseenter', function() {
                    const tooltip = $('<div class="tooltip">' + tooltipText + '</div>');
                    $('body').append(tooltip);
                    
                    const elementOffset = element.offset();
                    const elementWidth = element.outerWidth();
                    const elementHeight = element.outerHeight();
                    const tooltipWidth = tooltip.outerWidth();
                    const tooltipHeight = tooltip.outerHeight();
                    
                    tooltip.css({
                        top: elementOffset.top - tooltipHeight - 10,
                        left: elementOffset.left + (elementWidth / 2) - (tooltipWidth / 2)
                    }).fadeIn(200);
                });
                
                element.on('mouseleave', function() {
                    $('.tooltip').fadeOut(200, function() {
                        $(this).remove();
                    });
                });
            });
        },
        
        // Show notification
        showNotification: function(message, type) {
            const notification = $(`
                <div class="notification notification-${type}">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close">&times;</button>
                </div>
            `);
            
            $('body').append(notification);
            
            setTimeout(function() {
                notification.addClass('show');
            }, 100);
            
            // Auto hide after 5 seconds
            setTimeout(function() {
                notification.removeClass('show');
                setTimeout(function() {
                    notification.remove();
                }, 300);
            }, 5000);
            
            // Manual close
            notification.find('.notification-close').on('click', function() {
                notification.removeClass('show');
                setTimeout(function() {
                    notification.remove();
                }, 300);
            });
        },
        
        // Utility functions
        utils: {
            // Debounce function
            debounce: function(func, wait, immediate) {
                let timeout;
                return function() {
                    const context = this;
                    const args = arguments;
                    const later = function() {
                        timeout = null;
                        if (!immediate) func.apply(context, args);
                    };
                    const callNow = immediate && !timeout;
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                    if (callNow) func.apply(context, args);
                };
            },
            
            // Throttle function
            throttle: function(func, limit) {
                let inThrottle;
                return function() {
                    const args = arguments;
                    const context = this;
                    if (!inThrottle) {
                        func.apply(context, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                };
            },
            
            // Format number
            formatNumber: function(num) {
                if (num >= 1000000) {
                    return (num / 1000000).toFixed(1) + 'M';
                } else if (num >= 1000) {
                    return (num / 1000).toFixed(1) + 'K';
                }
                return num.toString();
            },
            
            // Get reading time
            getReadingTime: function(text) {
                const wordsPerMinute = 225;
                const words = text.trim().split(/\s+/).length;
                const readingTime = Math.ceil(words / wordsPerMinute);
                return Math.max(1, readingTime);
            }
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        EpicTheme.init();
    });
    
    // Back to top button
    $(window).on('scroll', function() {
        const backToTop = $('#back-to-top');
        if ($(window).scrollTop() > 300) {
            backToTop.fadeIn();
        } else {
            backToTop.fadeOut();
        }
    });
    
    $('#back-to-top').on('click', function() {
        $('html, body').animate({
            scrollTop: 0
        }, 600);
    });
    
    // User menu dropdown
    $('.user-menu-toggle').on('click', function() {
        $(this).next('.user-dropdown').slideToggle(200);
    });
    
    // Close user menu when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.user-menu').length) {
            $('.user-dropdown').slideUp(200);
        }
    });
    
    // Make theme object globally available
    window.EpicTheme = EpicTheme;
    
})(jQuery);
