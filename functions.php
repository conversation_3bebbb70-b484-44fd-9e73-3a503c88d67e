<?php
/**
 * Epic Novel Theme Functions
 * 
 * @package EpicNovelTheme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Theme constants
define('EPIC_THEME_VERSION', '1.0.0');
define('EPIC_THEME_PATH', get_template_directory());
define('EPIC_THEME_URL', get_template_directory_uri());

/**
 * Theme Setup
 */
function epic_theme_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('customize-selective-refresh-widgets');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'epic-novel-theme'),
        'footer' => __('Footer Menu', 'epic-novel-theme'),
    ));
    
    // Add image sizes
    add_image_size('novel-thumbnail', 300, 400, true);
    add_image_size('novel-cover', 600, 800, true);
    
    // Load text domain
    load_theme_textdomain('epic-novel-theme', EPIC_THEME_PATH . '/languages');
}
add_action('after_setup_theme', 'epic_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function epic_theme_scripts() {
    // Enqueue styles
    wp_enqueue_style('epic-theme-style', get_stylesheet_uri(), array(), EPIC_THEME_VERSION);
    
    // Enqueue scripts
    wp_enqueue_script('epic-theme-main', EPIC_THEME_URL . '/assets/js/main.js', array('jquery'), EPIC_THEME_VERSION, true);
    
    // Localize script for AJAX
    wp_localize_script('epic-theme-main', 'epic_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('epic_nonce'),
        'user_id' => get_current_user_id(),
    ));
    
    // Google Fonts
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&display=swap', array(), null);
    
    // Font Awesome for icons
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0');
}
add_action('wp_enqueue_scripts', 'epic_theme_scripts');

/**
 * Register Custom Post Types
 */
function epic_register_post_types() {
    // Register Novels post type
    $novel_labels = array(
        'name' => __('Novels', 'epic-novel-theme'),
        'singular_name' => __('Novel', 'epic-novel-theme'),
        'menu_name' => __('Novels', 'epic-novel-theme'),
        'add_new' => __('Add New Novel', 'epic-novel-theme'),
        'add_new_item' => __('Add New Novel', 'epic-novel-theme'),
        'edit_item' => __('Edit Novel', 'epic-novel-theme'),
        'new_item' => __('New Novel', 'epic-novel-theme'),
        'view_item' => __('View Novel', 'epic-novel-theme'),
        'search_items' => __('Search Novels', 'epic-novel-theme'),
        'not_found' => __('No novels found', 'epic-novel-theme'),
        'not_found_in_trash' => __('No novels found in trash', 'epic-novel-theme'),
    );
    
    $novel_args = array(
        'labels' => $novel_labels,
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'novel'),
        'capability_type' => 'post',
        'has_archive' => true,
        'hierarchical' => false,
        'menu_position' => 5,
        'menu_icon' => 'dashicons-book-alt',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'show_in_rest' => true,
    );
    
    register_post_type('novel', $novel_args);
}
add_action('init', 'epic_register_post_types');

/**
 * Register Custom Taxonomies
 */
function epic_register_taxonomies() {
    // Register Writers taxonomy
    register_taxonomy('writer', 'novel', array(
        'labels' => array(
            'name' => __('Writers', 'epic-novel-theme'),
            'singular_name' => __('Writer', 'epic-novel-theme'),
            'menu_name' => __('Writers', 'epic-novel-theme'),
        ),
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'writer'),
    ));
    
    // Register Genres taxonomy
    register_taxonomy('genre', 'novel', array(
        'labels' => array(
            'name' => __('Genres', 'epic-novel-theme'),
            'singular_name' => __('Genre', 'epic-novel-theme'),
            'menu_name' => __('Genres', 'epic-novel-theme'),
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'genre'),
    ));
    
    // Register Novel Tags taxonomy
    register_taxonomy('novel_tag', 'novel', array(
        'labels' => array(
            'name' => __('Novel Tags', 'epic-novel-theme'),
            'singular_name' => __('Novel Tag', 'epic-novel-theme'),
            'menu_name' => __('Tags', 'epic-novel-theme'),
        ),
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'novel-tag'),
    ));
    
    // Register Artists taxonomy
    register_taxonomy('artist', 'novel', array(
        'labels' => array(
            'name' => __('Artists', 'epic-novel-theme'),
            'singular_name' => __('Artist', 'epic-novel-theme'),
            'menu_name' => __('Artists', 'epic-novel-theme'),
        ),
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'artist'),
    ));
    
    // Register Types taxonomy
    register_taxonomy('novel_type', 'novel', array(
        'labels' => array(
            'name' => __('Types', 'epic-novel-theme'),
            'singular_name' => __('Type', 'epic-novel-theme'),
            'menu_name' => __('Types', 'epic-novel-theme'),
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'type'),
    ));
    
    // Register Chapter taxonomies for posts
    register_taxonomy('novel_title', 'post', array(
        'labels' => array(
            'name' => __('Novel Title', 'epic-novel-theme'),
            'singular_name' => __('Novel Title', 'epic-novel-theme'),
        ),
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'chapter-novel'),
    ));
}
add_action('init', 'epic_register_taxonomies');

/**
 * Include additional theme files
 */
require_once EPIC_THEME_PATH . '/inc/custom-fields.php';
require_once EPIC_THEME_PATH . '/inc/user-functions.php';
require_once EPIC_THEME_PATH . '/inc/bookmark-system.php';
require_once EPIC_THEME_PATH . '/inc/reading-history.php';
require_once EPIC_THEME_PATH . '/inc/google-oauth.php';
require_once EPIC_THEME_PATH . '/inc/ajax-handlers.php';
require_once EPIC_THEME_PATH . '/inc/seo-functions.php';
require_once EPIC_THEME_PATH . '/inc/admin-functions.php';

/**
 * Create database tables on theme activation
 */
function epic_theme_activation() {
    epic_create_database_tables();
    flush_rewrite_rules();
}
register_activation_hook(__FILE__, 'epic_theme_activation');

/**
 * Flush rewrite rules on theme switch
 */
function epic_theme_after_switch() {
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'epic_theme_after_switch');

/**
 * Fallback menu for primary navigation
 */
function epic_fallback_menu() {
    echo '<ul id="primary-menu" class="menu">';
    echo '<li><a href="' . home_url() . '">' . __('Home', 'epic-novel-theme') . '</a></li>';
    echo '<li><a href="' . get_post_type_archive_link('novel') . '">' . __('Novels', 'epic-novel-theme') . '</a></li>';

    // Add genre links
    $genres = get_terms(array(
        'taxonomy' => 'genre',
        'hide_empty' => true,
        'number' => 5
    ));

    if ($genres && !is_wp_error($genres)) {
        foreach ($genres as $genre) {
            echo '<li><a href="' . get_term_link($genre) . '">' . esc_html($genre->name) . '</a></li>';
        }
    }

    echo '</ul>';
}

/**
 * Add widget areas
 */
function epic_widgets_init() {
    register_sidebar(array(
        'name'          => __('Footer Widget 1', 'epic-novel-theme'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here to appear in your footer.', 'epic-novel-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => __('Footer Widget 2', 'epic-novel-theme'),
        'id'            => 'footer-2',
        'description'   => __('Add widgets here to appear in your footer.', 'epic-novel-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => __('Footer Widget 3', 'epic-novel-theme'),
        'id'            => 'footer-3',
        'description'   => __('Add widgets here to appear in your footer.', 'epic-novel-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => __('Footer Widget 4', 'epic-novel-theme'),
        'id'            => 'footer-4',
        'description'   => __('Add widgets here to appear in your footer.', 'epic-novel-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'epic_widgets_init');

/**
 * Create dashboard page on theme activation
 */
function epic_create_dashboard_page() {
    // Check if dashboard page exists
    $dashboard_page = get_page_by_path('dashboard');

    if (!$dashboard_page) {
        // Create dashboard page
        $page_data = array(
            'post_title' => __('Dashboard', 'epic-novel-theme'),
            'post_content' => '',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'dashboard'
        );

        wp_insert_post($page_data);
    }
}
add_action('after_switch_theme', 'epic_create_dashboard_page');
