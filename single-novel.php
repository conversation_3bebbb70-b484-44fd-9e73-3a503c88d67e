<?php
/**
 * The template for displaying single novels
 * 
 * @package EpicNovelTheme
 */

get_header(); ?>

<div class="container">
    <main id="main" class="site-main">
        
        <?php while (have_posts()) : the_post(); ?>
            
            <article id="novel-<?php the_ID(); ?>" <?php post_class('novel-single'); ?>>
                
                <div class="novel-header">
                    <div class="novel-cover">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('novel-cover', array('class' => 'novel-cover-image')); ?>
                        <?php else : ?>
                            <div class="novel-cover-placeholder">
                                <i class="fas fa-book"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="novel-info">
                        <h1 class="novel-title"><?php the_title(); ?></h1>
                        
                        <?php
                        $alternative_title = get_post_meta(get_the_ID(), 'alternative_title', true);
                        if ($alternative_title) :
                        ?>
                            <p class="alternative-title"><?php echo esc_html($alternative_title); ?></p>
                        <?php endif; ?>
                        
                        <div class="novel-meta">
                            <?php
                            $writers = get_the_terms(get_the_ID(), 'writer');
                            if ($writers && !is_wp_error($writers)) :
                            ?>
                                <div class="novel-writers">
                                    <span class="meta-label"><?php _e('Author(s):', 'epic-novel-theme'); ?></span>
                                    <?php
                                    $writer_links = array();
                                    foreach ($writers as $writer) {
                                        $writer_links[] = '<a href="' . get_term_link($writer) . '">' . esc_html($writer->name) . '</a>';
                                    }
                                    echo implode(', ', $writer_links);
                                    ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $artists = get_the_terms(get_the_ID(), 'artist');
                            if ($artists && !is_wp_error($artists)) :
                            ?>
                                <div class="novel-artists">
                                    <span class="meta-label"><?php _e('Artist(s):', 'epic-novel-theme'); ?></span>
                                    <?php
                                    $artist_links = array();
                                    foreach ($artists as $artist) {
                                        $artist_links[] = '<a href="' . get_term_link($artist) . '">' . esc_html($artist->name) . '</a>';
                                    }
                                    echo implode(', ', $artist_links);
                                    ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $novel_status = get_post_meta(get_the_ID(), 'novel_status', true);
                            if ($novel_status) :
                            ?>
                                <div class="novel-status">
                                    <span class="meta-label"><?php _e('Status:', 'epic-novel-theme'); ?></span>
                                    <span class="status-badge status-<?php echo esc_attr($novel_status); ?>">
                                        <?php
                                        $status_labels = array(
                                            'ongoing' => __('Ongoing', 'epic-novel-theme'),
                                            'completed' => __('Completed', 'epic-novel-theme'),
                                            'hiatus' => __('Hiatus', 'epic-novel-theme'),
                                            'dropped' => __('Dropped', 'epic-novel-theme')
                                        );
                                        echo esc_html($status_labels[$novel_status] ?? $novel_status);
                                        ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $release_year = get_post_meta(get_the_ID(), 'release_year', true);
                            if ($release_year) :
                            ?>
                                <div class="release-year">
                                    <span class="meta-label"><?php _e('Release Year:', 'epic-novel-theme'); ?></span>
                                    <?php echo esc_html($release_year); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $native_language = get_post_meta(get_the_ID(), 'native_language', true);
                            if ($native_language) :
                            ?>
                                <div class="native-language">
                                    <span class="meta-label"><?php _e('Original Language:', 'epic-novel-theme'); ?></span>
                                    <?php echo esc_html(ucfirst($native_language)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="novel-rating">
                            <?php
                            $rating = get_post_meta(get_the_ID(), 'rating', true);
                            $rating_count = get_post_meta(get_the_ID(), 'rating_count', true);
                            
                            if ($rating) :
                            ?>
                                <div class="rating-display">
                                    <div class="stars">
                                        <?php
                                        $full_stars = floor($rating / 2);
                                        $half_star = ($rating / 2) - $full_stars >= 0.5;
                                        
                                        for ($i = 1; $i <= 5; $i++) {
                                            if ($i <= $full_stars) {
                                                echo '<i class="fas fa-star"></i>';
                                            } elseif ($i == $full_stars + 1 && $half_star) {
                                                echo '<i class="fas fa-star-half-alt"></i>';
                                            } else {
                                                echo '<i class="far fa-star"></i>';
                                            }
                                        }
                                        ?>
                                    </div>
                                    <span class="rating-text">
                                        <?php echo number_format($rating, 1); ?>/10 
                                        (<?php printf(_n('%s rating', '%s ratings', $rating_count, 'epic-novel-theme'), number_format($rating_count)); ?>)
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (is_user_logged_in()) : ?>
                                <div class="user-rating">
                                    <button class="rate-novel-btn" data-novel-id="<?php the_ID(); ?>">
                                        <i class="fas fa-star"></i>
                                        <?php _e('Rate this Novel', 'epic-novel-theme'); ?>
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="novel-actions">
                            <?php if (is_user_logged_in()) : ?>
                                <button class="bookmark-btn <?php echo epic_is_bookmarked(get_current_user_id(), get_the_ID()) ? 'bookmarked' : ''; ?>" 
                                        data-post-id="<?php the_ID(); ?>">
                                    <i class="fas fa-bookmark"></i>
                                    <?php _e('Bookmark', 'epic-novel-theme'); ?>
                                </button>
                            <?php endif; ?>
                            
                            <button class="share-btn" data-url="<?php the_permalink(); ?>" data-title="<?php the_title_attribute(); ?>">
                                <i class="fas fa-share-alt"></i>
                                <?php _e('Share', 'epic-novel-theme'); ?>
                            </button>
                            
                            <?php
                            $novelupdates_url = get_post_meta(get_the_ID(), 'novelupdates_url', true);
                            if ($novelupdates_url) :
                            ?>
                                <a href="<?php echo esc_url($novelupdates_url); ?>" target="_blank" class="external-link-btn">
                                    <i class="fas fa-external-link-alt"></i>
                                    <?php _e('NovelUpdates', 'epic-novel-theme'); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Ad Space -->
                <div class="ad-space header-ad">
                    <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
                </div>
                
                <div class="novel-content">
                    <div class="novel-main">
                        
                        <!-- Novel Description -->
                        <div class="novel-description">
                            <h2><?php _e('Description', 'epic-novel-theme'); ?></h2>
                            <div class="description-content">
                                <?php
                                $novel_summary = get_post_meta(get_the_ID(), 'novel_summary', true);
                                if ($novel_summary) {
                                    echo wp_kses_post(wpautop($novel_summary));
                                } else {
                                    the_content();
                                }
                                ?>
                            </div>
                        </div>
                        
                        <!-- Genres and Tags -->
                        <div class="novel-taxonomies">
                            <?php
                            $genres = get_the_terms(get_the_ID(), 'genre');
                            if ($genres && !is_wp_error($genres)) :
                            ?>
                                <div class="novel-genres">
                                    <h3><?php _e('Genres', 'epic-novel-theme'); ?></h3>
                                    <div class="taxonomy-list">
                                        <?php foreach ($genres as $genre) : ?>
                                            <a href="<?php echo get_term_link($genre); ?>" class="taxonomy-tag genre-tag">
                                                <?php echo esc_html($genre->name); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php
                            $novel_tags = get_the_terms(get_the_ID(), 'novel_tag');
                            if ($novel_tags && !is_wp_error($novel_tags)) :
                            ?>
                                <div class="novel-tags">
                                    <h3><?php _e('Tags', 'epic-novel-theme'); ?></h3>
                                    <div class="taxonomy-list">
                                        <?php foreach ($novel_tags as $tag) : ?>
                                            <a href="<?php echo get_term_link($tag); ?>" class="taxonomy-tag novel-tag">
                                                <?php echo esc_html($tag->name); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Chapter List -->
                        <div class="novel-chapters">
                            <div class="chapters-header">
                                <h2><?php _e('Chapters', 'epic-novel-theme'); ?></h2>
                                <div class="chapters-controls">
                                    <select id="chapter-sort">
                                        <option value="asc"><?php _e('Oldest First', 'epic-novel-theme'); ?></option>
                                        <option value="desc"><?php _e('Newest First', 'epic-novel-theme'); ?></option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="chapters-list" id="chapters-list">
                                <div class="loading-chapters">
                                    <div class="spinner"></div>
                                    <p><?php _e('Loading chapters...', 'epic-novel-theme'); ?></p>
                                </div>
                            </div>
                            
                            <div class="chapters-pagination" id="chapters-pagination" style="display: none;">
                                <button class="load-more-chapters" id="load-more-chapters">
                                    <?php _e('Load More Chapters', 'epic-novel-theme'); ?>
                                </button>
                            </div>
                        </div>
                        
                    </div>
                    
                    <aside class="novel-sidebar">
                        
                        <!-- Novel Statistics -->
                        <div class="novel-stats-widget">
                            <h3><?php _e('Statistics', 'epic-novel-theme'); ?></h3>
                            <div class="stats-list" id="novel-stats">
                                <div class="loading-stats">
                                    <div class="spinner"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Ad Space - Sidebar -->
                        <div class="ad-space sidebar-ad">
                            <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
                        </div>
                        
                        <!-- Related Novels -->
                        <div class="related-novels-widget">
                            <h3><?php _e('Related Novels', 'epic-novel-theme'); ?></h3>
                            <?php
                            $related_novels = epic_get_related_novels(get_the_ID(), 5);
                            if ($related_novels) :
                            ?>
                                <div class="related-novels-list">
                                    <?php foreach ($related_novels as $related) : ?>
                                        <div class="related-novel-item">
                                            <a href="<?php echo get_permalink($related->ID); ?>">
                                                <?php if (has_post_thumbnail($related->ID)) : ?>
                                                    <?php echo get_the_post_thumbnail($related->ID, 'thumbnail'); ?>
                                                <?php endif; ?>
                                                <div class="related-novel-info">
                                                    <h4><?php echo esc_html($related->post_title); ?></h4>
                                                    <p><?php echo wp_trim_words($related->post_excerpt, 15); ?></p>
                                                </div>
                                            </a>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else : ?>
                                <p><?php _e('No related novels found.', 'epic-novel-theme'); ?></p>
                            <?php endif; ?>
                        </div>
                        
                    </aside>
                </div>
                
            </article>
            
        <?php endwhile; ?>
        
    </main>
</div>

<!-- Rating Modal -->
<div class="rating-modal" id="rating-modal" style="display: none;">
    <div class="rating-modal-content">
        <div class="rating-modal-header">
            <h3><?php _e('Rate this Novel', 'epic-novel-theme'); ?></h3>
            <button class="close-rating-modal">&times;</button>
        </div>
        
        <div class="rating-modal-body">
            <p><?php _e('How would you rate this novel?', 'epic-novel-theme'); ?></p>
            <div class="rating-stars" id="rating-stars">
                <?php for ($i = 1; $i <= 10; $i++) : ?>
                    <button class="rating-star" data-rating="<?php echo $i; ?>">
                        <i class="far fa-star"></i>
                    </button>
                <?php endfor; ?>
            </div>
            <div class="rating-value" id="rating-value">0/10</div>
        </div>
        
        <div class="rating-modal-footer">
            <button class="submit-rating" id="submit-rating" disabled>
                <?php _e('Submit Rating', 'epic-novel-theme'); ?>
            </button>
            <button class="cancel-rating">
                <?php _e('Cancel', 'epic-novel-theme'); ?>
            </button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    let currentPage = 1;
    let hasMoreChapters = true;
    let currentSort = 'asc';
    
    // Load chapters
    function loadChapters(page = 1, sort = 'asc', append = false) {
        $.ajax({
            url: epic_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'epic_get_novel_chapters',
                nonce: epic_ajax.nonce,
                novel_title: '<?php echo esc_js(get_the_title()); ?>',
                page: page,
                sort: sort
            },
            success: function(response) {
                if (response.success) {
                    const chapters = response.data.chapters;
                    hasMoreChapters = response.data.has_more;
                    
                    let html = '';
                    chapters.forEach(function(chapter) {
                        html += `
                            <div class="chapter-item">
                                <div class="chapter-info">
                                    <h4><a href="${chapter.url}">Chapter ${chapter.chapter_number}: ${chapter.title}</a></h4>
                                    <div class="chapter-meta">
                                        <span class="chapter-date">${chapter.date}</span>
                                        <span class="reading-time">${chapter.reading_time} min read</span>
                                    </div>
                                </div>
                                <div class="chapter-actions">
                                    <a href="${chapter.url}" class="read-btn">Read</a>
                                    ${chapter.is_bookmarked ? '<button class="bookmark-btn bookmarked" data-post-id="' + chapter.id + '"><i class="fas fa-bookmark"></i></button>' : '<button class="bookmark-btn" data-post-id="' + chapter.id + '"><i class="far fa-bookmark"></i></button>'}
                                </div>
                            </div>
                        `;
                    });
                    
                    if (append) {
                        $('#chapters-list').append(html);
                    } else {
                        $('#chapters-list').html(html);
                    }
                    
                    if (hasMoreChapters) {
                        $('#chapters-pagination').show();
                    } else {
                        $('#chapters-pagination').hide();
                    }
                    
                    if (chapters.length === 0 && page === 1) {
                        $('#chapters-list').html('<p class="no-chapters"><?php _e('No chapters available yet.', 'epic-novel-theme'); ?></p>');
                    }
                }
            }
        });
    }
    
    // Load novel statistics
    function loadNovelStats() {
        $.ajax({
            url: epic_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'epic_get_novel_stats',
                nonce: epic_ajax.nonce,
                novel_id: <?php echo get_the_ID(); ?>
            },
            success: function(response) {
                if (response.success) {
                    const stats = response.data;
                    let html = `
                        <div class="stat-item">
                            <span class="stat-label"><?php _e('Views:', 'epic-novel-theme'); ?></span>
                            <span class="stat-value">${stats.view_count.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label"><?php _e('Bookmarks:', 'epic-novel-theme'); ?></span>
                            <span class="stat-value">${stats.bookmark_count.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label"><?php _e('Chapters:', 'epic-novel-theme'); ?></span>
                            <span class="stat-value">${stats.chapter_count}</span>
                        </div>
                    `;
                    
                    if (stats.rating > 0) {
                        html += `
                            <div class="stat-item">
                                <span class="stat-label"><?php _e('Rating:', 'epic-novel-theme'); ?></span>
                                <span class="stat-value">${stats.rating}/10 (${stats.rating_count})</span>
                            </div>
                        `;
                    }
                    
                    $('#novel-stats').html(html);
                }
            }
        });
    }
    
    // Initial loads
    loadChapters();
    loadNovelStats();
    
    // Chapter sorting
    $('#chapter-sort').on('change', function() {
        currentSort = $(this).val();
        currentPage = 1;
        loadChapters(currentPage, currentSort);
    });
    
    // Load more chapters
    $('#load-more-chapters').on('click', function() {
        currentPage++;
        loadChapters(currentPage, currentSort, true);
    });
    
    // Rating functionality
    $('.rate-novel-btn').on('click', function() {
        $('#rating-modal').show();
    });
    
    $('.close-rating-modal, .cancel-rating').on('click', function() {
        $('#rating-modal').hide();
        resetRating();
    });
    
    $('.rating-star').on('click', function() {
        const rating = $(this).data('rating');
        selectRating(rating);
    });
    
    function selectRating(rating) {
        $('.rating-star').each(function(index) {
            const star = $(this).find('i');
            if (index < rating) {
                star.removeClass('far').addClass('fas');
            } else {
                star.removeClass('fas').addClass('far');
            }
        });
        
        $('#rating-value').text(rating + '/10');
        $('#submit-rating').prop('disabled', false).data('rating', rating);
    }
    
    function resetRating() {
        $('.rating-star i').removeClass('fas').addClass('far');
        $('#rating-value').text('0/10');
        $('#submit-rating').prop('disabled', true).removeData('rating');
    }
    
    $('#submit-rating').on('click', function() {
        const rating = $(this).data('rating');
        
        $.ajax({
            url: epic_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'epic_rate_novel',
                nonce: epic_ajax.nonce,
                novel_id: <?php echo get_the_ID(); ?>,
                rating: rating
            },
            success: function(response) {
                if (response.success) {
                    $('#rating-modal').hide();
                    alert('<?php _e('Thank you for rating this novel!', 'epic-novel-theme'); ?>');
                    location.reload(); // Refresh to show updated rating
                } else {
                    alert(response.data);
                }
            }
        });
    });
});
</script>

<?php
get_footer();
?>
