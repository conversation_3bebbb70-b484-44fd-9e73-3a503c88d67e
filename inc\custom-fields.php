<?php
/**
 * Custom Fields for Novels and Chapters
 * 
 * @package EpicNovelTheme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add custom meta boxes for novels
 */
function epic_add_novel_meta_boxes() {
    add_meta_box(
        'epic_novel_details',
        __('Novel Details', 'epic-novel-theme'),
        'epic_novel_details_callback',
        'novel',
        'normal',
        'high'
    );
    
    add_meta_box(
        'epic_novel_status',
        __('Novel Status', 'epic-novel-theme'),
        'epic_novel_status_callback',
        'novel',
        'side',
        'default'
    );
}
add_action('add_meta_boxes', 'epic_add_novel_meta_boxes');

/**
 * Novel details meta box callback
 */
function epic_novel_details_callback($post) {
    wp_nonce_field('epic_novel_details_nonce', 'epic_novel_details_nonce');
    
    $alternative_title = get_post_meta($post->ID, 'alternative_title', true);
    $native_language = get_post_meta($post->ID, 'native_language', true);
    $release_year = get_post_meta($post->ID, 'release_year', true);
    $novelupdates_url = get_post_meta($post->ID, 'novelupdates_url', true);
    $novel_summary = get_post_meta($post->ID, 'novel_summary', true);
    $total_chapters = get_post_meta($post->ID, 'total_chapters', true);
    $reading_difficulty = get_post_meta($post->ID, 'reading_difficulty', true);
    ?>
    
    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="alternative_title"><?php _e('Alternative Title', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <input type="text" id="alternative_title" name="alternative_title" value="<?php echo esc_attr($alternative_title); ?>" class="regular-text" />
                <p class="description"><?php _e('Alternative or translated title of the novel.', 'epic-novel-theme'); ?></p>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="native_language"><?php _e('Native Language', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <select id="native_language" name="native_language">
                    <option value=""><?php _e('Select Language', 'epic-novel-theme'); ?></option>
                    <option value="japanese" <?php selected($native_language, 'japanese'); ?>><?php _e('Japanese', 'epic-novel-theme'); ?></option>
                    <option value="korean" <?php selected($native_language, 'korean'); ?>><?php _e('Korean', 'epic-novel-theme'); ?></option>
                    <option value="chinese" <?php selected($native_language, 'chinese'); ?>><?php _e('Chinese', 'epic-novel-theme'); ?></option>
                    <option value="english" <?php selected($native_language, 'english'); ?>><?php _e('English', 'epic-novel-theme'); ?></option>
                    <option value="other" <?php selected($native_language, 'other'); ?>><?php _e('Other', 'epic-novel-theme'); ?></option>
                </select>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="release_year"><?php _e('Release Year', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <input type="number" id="release_year" name="release_year" value="<?php echo esc_attr($release_year); ?>" min="1900" max="<?php echo date('Y'); ?>" />
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="novelupdates_url"><?php _e('NovelUpdates URL', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <input type="url" id="novelupdates_url" name="novelupdates_url" value="<?php echo esc_url($novelupdates_url); ?>" class="regular-text" />
                <p class="description"><?php _e('Link to the novel on NovelUpdates.', 'epic-novel-theme'); ?></p>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="total_chapters"><?php _e('Total Chapters', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <input type="number" id="total_chapters" name="total_chapters" value="<?php echo esc_attr($total_chapters); ?>" min="0" />
                <p class="description"><?php _e('Total number of chapters (leave empty if ongoing).', 'epic-novel-theme'); ?></p>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="reading_difficulty"><?php _e('Reading Difficulty', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <select id="reading_difficulty" name="reading_difficulty">
                    <option value=""><?php _e('Select Difficulty', 'epic-novel-theme'); ?></option>
                    <option value="beginner" <?php selected($reading_difficulty, 'beginner'); ?>><?php _e('Beginner', 'epic-novel-theme'); ?></option>
                    <option value="intermediate" <?php selected($reading_difficulty, 'intermediate'); ?>><?php _e('Intermediate', 'epic-novel-theme'); ?></option>
                    <option value="advanced" <?php selected($reading_difficulty, 'advanced'); ?>><?php _e('Advanced', 'epic-novel-theme'); ?></option>
                </select>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="novel_summary"><?php _e('Novel Summary', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <textarea id="novel_summary" name="novel_summary" rows="5" class="large-text"><?php echo esc_textarea($novel_summary); ?></textarea>
                <p class="description"><?php _e('Detailed summary of the novel.', 'epic-novel-theme'); ?></p>
            </td>
        </tr>
    </table>
    
    <?php
}

/**
 * Novel status meta box callback
 */
function epic_novel_status_callback($post) {
    $novel_status = get_post_meta($post->ID, 'novel_status', true);
    $last_updated = get_post_meta($post->ID, 'last_updated', true);
    $rating = get_post_meta($post->ID, 'rating', true);
    $view_count = get_post_meta($post->ID, 'view_count', true);
    ?>
    
    <p>
        <label for="novel_status"><strong><?php _e('Status', 'epic-novel-theme'); ?></strong></label><br>
        <select id="novel_status" name="novel_status" style="width: 100%;">
            <option value="ongoing" <?php selected($novel_status, 'ongoing'); ?>><?php _e('Ongoing', 'epic-novel-theme'); ?></option>
            <option value="completed" <?php selected($novel_status, 'completed'); ?>><?php _e('Completed', 'epic-novel-theme'); ?></option>
            <option value="hiatus" <?php selected($novel_status, 'hiatus'); ?>><?php _e('Hiatus', 'epic-novel-theme'); ?></option>
            <option value="dropped" <?php selected($novel_status, 'dropped'); ?>><?php _e('Dropped', 'epic-novel-theme'); ?></option>
        </select>
    </p>
    
    <p>
        <label for="rating"><strong><?php _e('Rating', 'epic-novel-theme'); ?></strong></label><br>
        <input type="number" id="rating" name="rating" value="<?php echo esc_attr($rating); ?>" min="0" max="10" step="0.1" style="width: 100%;" />
        <small><?php _e('Rating out of 10', 'epic-novel-theme'); ?></small>
    </p>
    
    <p>
        <label for="last_updated"><strong><?php _e('Last Updated', 'epic-novel-theme'); ?></strong></label><br>
        <input type="date" id="last_updated" name="last_updated" value="<?php echo esc_attr($last_updated); ?>" style="width: 100%;" />
    </p>
    
    <p>
        <strong><?php _e('View Count', 'epic-novel-theme'); ?></strong><br>
        <span><?php echo esc_html($view_count ? $view_count : '0'); ?></span>
    </p>
    
    <?php
}

/**
 * Add custom meta boxes for chapters (posts)
 */
function epic_add_chapter_meta_boxes() {
    add_meta_box(
        'epic_chapter_details',
        __('Chapter Details', 'epic-novel-theme'),
        'epic_chapter_details_callback',
        'post',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'epic_add_chapter_meta_boxes');

/**
 * Chapter details meta box callback
 */
function epic_chapter_details_callback($post) {
    wp_nonce_field('epic_chapter_details_nonce', 'epic_chapter_details_nonce');
    
    $novel_title = get_post_meta($post->ID, 'novel_title', true);
    $volume_title = get_post_meta($post->ID, 'volume_title', true);
    $chapter_number = get_post_meta($post->ID, 'chapter_number', true);
    $chapter_title = get_post_meta($post->ID, 'chapter_title', true);
    $translator_notes = get_post_meta($post->ID, 'translator_notes', true);
    ?>
    
    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="novel_title"><?php _e('Novel Title', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <select id="novel_title" name="novel_title" class="regular-text">
                    <option value=""><?php _e('Select Novel', 'epic-novel-theme'); ?></option>
                    <?php
                    $novels = get_posts(array(
                        'post_type' => 'novel',
                        'posts_per_page' => -1,
                        'post_status' => 'publish',
                        'orderby' => 'title',
                        'order' => 'ASC'
                    ));
                    
                    foreach ($novels as $novel) {
                        $selected = selected($novel_title, $novel->post_title, false);
                        echo '<option value="' . esc_attr($novel->post_title) . '" ' . $selected . '>' . esc_html($novel->post_title) . '</option>';
                    }
                    ?>
                </select>
                <p class="description"><?php _e('Select the novel this chapter belongs to.', 'epic-novel-theme'); ?></p>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="volume_title"><?php _e('Volume Title', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <input type="text" id="volume_title" name="volume_title" value="<?php echo esc_attr($volume_title); ?>" class="regular-text" />
                <p class="description"><?php _e('Volume or arc title (optional).', 'epic-novel-theme'); ?></p>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="chapter_number"><?php _e('Chapter Number', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <input type="text" id="chapter_number" name="chapter_number" value="<?php echo esc_attr($chapter_number); ?>" class="regular-text" />
                <p class="description"><?php _e('Chapter number (e.g., 1, 2.5, Prologue, Epilogue).', 'epic-novel-theme'); ?></p>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="chapter_title"><?php _e('Chapter Title', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <input type="text" id="chapter_title" name="chapter_title" value="<?php echo esc_attr($chapter_title); ?>" class="regular-text" />
                <p class="description"><?php _e('Specific title for this chapter (optional).', 'epic-novel-theme'); ?></p>
            </td>
        </tr>
        
        <tr>
            <th scope="row">
                <label for="translator_notes"><?php _e('Translator Notes', 'epic-novel-theme'); ?></label>
            </th>
            <td>
                <textarea id="translator_notes" name="translator_notes" rows="3" class="large-text"><?php echo esc_textarea($translator_notes); ?></textarea>
                <p class="description"><?php _e('Notes from the translator (optional).', 'epic-novel-theme'); ?></p>
            </td>
        </tr>
    </table>
    
    <?php
}

/**
 * Save novel meta data
 */
function epic_save_novel_meta($post_id) {
    // Check if nonce is valid
    if (!isset($_POST['epic_novel_details_nonce']) || !wp_verify_nonce($_POST['epic_novel_details_nonce'], 'epic_novel_details_nonce')) {
        return;
    }
    
    // Check if user has permission to edit the post
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    // Check if not an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    // Save novel details
    $fields = array(
        'alternative_title',
        'native_language',
        'release_year',
        'novelupdates_url',
        'novel_summary',
        'total_chapters',
        'reading_difficulty',
        'novel_status',
        'rating',
        'last_updated'
    );
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
    
    // Handle novel summary separately for textarea
    if (isset($_POST['novel_summary'])) {
        update_post_meta($post_id, 'novel_summary', sanitize_textarea_field($_POST['novel_summary']));
    }
}
add_action('save_post_novel', 'epic_save_novel_meta');

/**
 * Save chapter meta data
 */
function epic_save_chapter_meta($post_id) {
    // Check if nonce is valid
    if (!isset($_POST['epic_chapter_details_nonce']) || !wp_verify_nonce($_POST['epic_chapter_details_nonce'], 'epic_chapter_details_nonce')) {
        return;
    }
    
    // Check if user has permission to edit the post
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    // Check if not an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    // Save chapter details
    $fields = array(
        'novel_title',
        'volume_title',
        'chapter_number',
        'chapter_title'
    );
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
    
    // Handle translator notes separately for textarea
    if (isset($_POST['translator_notes'])) {
        update_post_meta($post_id, 'translator_notes', sanitize_textarea_field($_POST['translator_notes']));
    }
}
add_action('save_post_post', 'epic_save_chapter_meta');
