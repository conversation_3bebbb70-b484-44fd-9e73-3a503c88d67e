<?php
/**
 * The sidebar containing the main widget area
 * 
 * @package EpicNovelTheme
 */

if (!is_active_sidebar('sidebar-1')) {
    return;
}
?>

<aside id="secondary" class="widget-area">
    
    <!-- Popular Novels Widget -->
    <div class="widget popular-novels-widget">
        <h3 class="widget-title"><?php _e('Popular Novels', 'epic-novel-theme'); ?></h3>
        <div class="widget-content">
            <?php
            $popular_novels = get_posts(array(
                'post_type' => 'novel',
                'posts_per_page' => 5,
                'meta_key' => 'view_count',
                'orderby' => 'meta_value_num',
                'order' => 'DESC',
                'post_status' => 'publish'
            ));
            
            if ($popular_novels) :
            ?>
                <ul class="popular-novels-list">
                    <?php foreach ($popular_novels as $novel) : ?>
                        <li class="popular-novel-item">
                            <a href="<?php echo get_permalink($novel->ID); ?>">
                                <?php if (has_post_thumbnail($novel->ID)) : ?>
                                    <?php echo get_the_post_thumbnail($novel->ID, 'thumbnail'); ?>
                                <?php endif; ?>
                                <div class="novel-info">
                                    <h4><?php echo esc_html($novel->post_title); ?></h4>
                                    <div class="novel-meta">
                                        <?php
                                        $view_count = get_post_meta($novel->ID, 'view_count', true);
                                        $rating = get_post_meta($novel->ID, 'rating', true);
                                        ?>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            <?php echo $view_count ? number_format($view_count) : '0'; ?>
                                        </span>
                                        <?php if ($rating) : ?>
                                            <span class="rating">
                                                <i class="fas fa-star"></i>
                                                <?php echo number_format($rating, 1); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php else : ?>
                <p><?php _e('No popular novels found.', 'epic-novel-theme'); ?></p>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Recent Chapters Widget -->
    <div class="widget recent-chapters-widget">
        <h3 class="widget-title"><?php _e('Recent Chapters', 'epic-novel-theme'); ?></h3>
        <div class="widget-content">
            <?php
            $recent_chapters = get_posts(array(
                'post_type' => 'post',
                'posts_per_page' => 5,
                'post_status' => 'publish',
                'orderby' => 'date',
                'order' => 'DESC'
            ));
            
            if ($recent_chapters) :
            ?>
                <ul class="recent-chapters-list">
                    <?php foreach ($recent_chapters as $chapter) : ?>
                        <li class="recent-chapter-item">
                            <a href="<?php echo get_permalink($chapter->ID); ?>">
                                <div class="chapter-info">
                                    <?php
                                    $novel_title = get_post_meta($chapter->ID, 'novel_title', true);
                                    $chapter_number = get_post_meta($chapter->ID, 'chapter_number', true);
                                    ?>
                                    
                                    <?php if ($novel_title) : ?>
                                        <h4 class="novel-title"><?php echo esc_html($novel_title); ?></h4>
                                    <?php endif; ?>
                                    
                                    <p class="chapter-title">
                                        <?php if ($chapter_number) : ?>
                                            <?php _e('Chapter', 'epic-novel-theme'); ?> <?php echo esc_html($chapter_number); ?>:
                                        <?php endif; ?>
                                        <?php echo esc_html($chapter->post_title); ?>
                                    </p>
                                    
                                    <span class="chapter-date">
                                        <?php echo human_time_diff(strtotime($chapter->post_date), current_time('timestamp')) . ' ago'; ?>
                                    </span>
                                </div>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php else : ?>
                <p><?php _e('No recent chapters found.', 'epic-novel-theme'); ?></p>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Genres Widget -->
    <div class="widget genres-widget">
        <h3 class="widget-title"><?php _e('Browse by Genre', 'epic-novel-theme'); ?></h3>
        <div class="widget-content">
            <?php
            $genres = get_terms(array(
                'taxonomy' => 'genre',
                'hide_empty' => true,
                'number' => 10,
                'orderby' => 'count',
                'order' => 'DESC'
            ));
            
            if ($genres && !is_wp_error($genres)) :
            ?>
                <div class="genres-cloud">
                    <?php foreach ($genres as $genre) : ?>
                        <a href="<?php echo get_term_link($genre); ?>" class="genre-link" 
                           title="<?php printf(__('%s novels', 'epic-novel-theme'), $genre->count); ?>">
                            <?php echo esc_html($genre->name); ?>
                            <span class="count">(<?php echo $genre->count; ?>)</span>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php else : ?>
                <p><?php _e('No genres found.', 'epic-novel-theme'); ?></p>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Ad Space - Sidebar -->
    <div class="widget ad-widget">
        <div class="ad-space sidebar-ad">
            <?php _e('Advertisement Space', 'epic-novel-theme'); ?>
        </div>
    </div>
    
    <!-- Reading Statistics Widget (for logged in users) -->
    <?php if (is_user_logged_in()) : ?>
        <div class="widget reading-stats-widget">
            <h3 class="widget-title"><?php _e('Your Reading Stats', 'epic-novel-theme'); ?></h3>
            <div class="widget-content">
                <?php
                global $wpdb;
                $user_id = get_current_user_id();
                $history_table = $wpdb->prefix . 'epic_reading_history';
                $bookmarks_table = $wpdb->prefix . 'epic_bookmarks';
                
                $chapters_read = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $history_table WHERE user_id = %d",
                    $user_id
                ));
                
                $bookmarks_count = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $bookmarks_table WHERE user_id = %d",
                    $user_id
                ));
                
                $reading_time = $wpdb->get_var($wpdb->prepare(
                    "SELECT SUM(reading_time) FROM $history_table WHERE user_id = %d",
                    $user_id
                ));
                
                $reading_hours = $reading_time ? round($reading_time / 3600, 1) : 0;
                ?>
                
                <div class="reading-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo number_format($chapters_read); ?></span>
                        <span class="stat-label"><?php _e('Chapters Read', 'epic-novel-theme'); ?></span>
                    </div>
                    
                    <div class="stat-item">
                        <span class="stat-number"><?php echo number_format($bookmarks_count); ?></span>
                        <span class="stat-label"><?php _e('Bookmarks', 'epic-novel-theme'); ?></span>
                    </div>
                    
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $reading_hours; ?>h</span>
                        <span class="stat-label"><?php _e('Reading Time', 'epic-novel-theme'); ?></span>
                    </div>
                </div>
                
                <div class="stats-actions">
                    <a href="<?php echo home_url('/dashboard'); ?>" class="dashboard-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <?php _e('View Dashboard', 'epic-novel-theme'); ?>
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Newsletter Signup Widget -->
    <div class="widget newsletter-widget">
        <h3 class="widget-title"><?php _e('Stay Updated', 'epic-novel-theme'); ?></h3>
        <div class="widget-content">
            <p><?php _e('Get notified when new chapters are released!', 'epic-novel-theme'); ?></p>
            
            <form class="newsletter-form" method="post" action="">
                <div class="form-group">
                    <input type="email" name="newsletter_email" placeholder="<?php _e('Enter your email', 'epic-novel-theme'); ?>" required>
                    <button type="submit" name="newsletter_subscribe">
                        <i class="fas fa-paper-plane"></i>
                        <?php _e('Subscribe', 'epic-novel-theme'); ?>
                    </button>
                </div>
                <p class="newsletter-note">
                    <?php _e('We respect your privacy. Unsubscribe at any time.', 'epic-novel-theme'); ?>
                </p>
            </form>
        </div>
    </div>
    
    <?php dynamic_sidebar('sidebar-1'); ?>
    
</aside>
