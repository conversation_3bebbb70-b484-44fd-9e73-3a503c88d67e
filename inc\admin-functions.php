<?php
/**
 * Admin Functions and Customizations
 * 
 * @package EpicNovelTheme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Customize admin menu for novels
 */
function epic_customize_admin_menu() {
    global $menu, $submenu;
    
    // Reorder novel submenu items
    if (isset($submenu['edit.php?post_type=novel'])) {
        $novel_submenu = $submenu['edit.php?post_type=novel'];
        
        // Reorder to match the required structure
        $new_submenu = array();
        
        // Add New Novel
        foreach ($novel_submenu as $key => $item) {
            if ($item[2] === 'post-new.php?post_type=novel') {
                $new_submenu[5] = $item;
                unset($novel_submenu[$key]);
                break;
            }
        }
        
        // Tags
        foreach ($novel_submenu as $key => $item) {
            if ($item[2] === 'edit-tags.php?taxonomy=novel_tag&post_type=novel') {
                $item[0] = __('Tags', 'epic-novel-theme');
                $new_submenu[10] = $item;
                unset($novel_submenu[$key]);
                break;
            }
        }
        
        // Genres
        foreach ($novel_submenu as $key => $item) {
            if ($item[2] === 'edit-tags.php?taxonomy=genre&post_type=novel') {
                $item[0] = __('Genres', 'epic-novel-theme');
                $new_submenu[15] = $item;
                unset($novel_submenu[$key]);
                break;
            }
        }
        
        // Writers
        foreach ($novel_submenu as $key => $item) {
            if ($item[2] === 'edit-tags.php?taxonomy=writer&post_type=novel') {
                $item[0] = __('Writers', 'epic-novel-theme');
                $new_submenu[20] = $item;
                unset($novel_submenu[$key]);
                break;
            }
        }
        
        // Artists
        foreach ($novel_submenu as $key => $item) {
            if ($item[2] === 'edit-tags.php?taxonomy=artist&post_type=novel') {
                $item[0] = __('Artists', 'epic-novel-theme');
                $new_submenu[25] = $item;
                unset($novel_submenu[$key]);
                break;
            }
        }
        
        // Types
        foreach ($novel_submenu as $key => $item) {
            if ($item[2] === 'edit-tags.php?taxonomy=novel_type&post_type=novel') {
                $item[0] = __('Types', 'epic-novel-theme');
                $new_submenu[30] = $item;
                unset($novel_submenu[$key]);
                break;
            }
        }
        
        // Add remaining items
        foreach ($novel_submenu as $key => $item) {
            $new_submenu[$key] = $item;
        }
        
        ksort($new_submenu);
        $submenu['edit.php?post_type=novel'] = $new_submenu;
    }
}
add_action('admin_menu', 'epic_customize_admin_menu', 999);

/**
 * Add custom admin columns for novels
 */
function epic_novel_admin_columns($columns) {
    $new_columns = array();
    
    foreach ($columns as $key => $value) {
        $new_columns[$key] = $value;
        
        if ($key === 'title') {
            $new_columns['novel_cover'] = __('Cover', 'epic-novel-theme');
            $new_columns['novel_status'] = __('Status', 'epic-novel-theme');
            $new_columns['novel_rating'] = __('Rating', 'epic-novel-theme');
            $new_columns['view_count'] = __('Views', 'epic-novel-theme');
            $new_columns['chapter_count'] = __('Chapters', 'epic-novel-theme');
        }
    }
    
    return $new_columns;
}
add_filter('manage_novel_posts_columns', 'epic_novel_admin_columns');

/**
 * Display custom admin column content for novels
 */
function epic_novel_admin_column_content($column, $post_id) {
    switch ($column) {
        case 'novel_cover':
            $thumbnail = get_the_post_thumbnail($post_id, array(50, 70));
            echo $thumbnail ?: '<span class="dashicons dashicons-book-alt"></span>';
            break;
            
        case 'novel_status':
            $status = get_post_meta($post_id, 'novel_status', true);
            $status_labels = array(
                'ongoing' => __('Ongoing', 'epic-novel-theme'),
                'completed' => __('Completed', 'epic-novel-theme'),
                'hiatus' => __('Hiatus', 'epic-novel-theme'),
                'dropped' => __('Dropped', 'epic-novel-theme')
            );
            
            $status_colors = array(
                'ongoing' => '#00a32a',
                'completed' => '#0073aa',
                'hiatus' => '#dba617',
                'dropped' => '#d63638'
            );
            
            if ($status && isset($status_labels[$status])) {
                $color = $status_colors[$status];
                echo '<span style="color: ' . $color . '; font-weight: bold;">' . $status_labels[$status] . '</span>';
            } else {
                echo '—';
            }
            break;
            
        case 'novel_rating':
            $rating = get_post_meta($post_id, 'rating', true);
            $rating_count = get_post_meta($post_id, 'rating_count', true);
            
            if ($rating) {
                echo '<span title="' . sprintf(__('%s ratings', 'epic-novel-theme'), $rating_count) . '">';
                echo number_format($rating, 1) . '/10';
                echo ' <span class="dashicons dashicons-star-filled" style="color: #ffb900; font-size: 16px;"></span>';
                echo '</span>';
            } else {
                echo '—';
            }
            break;
            
        case 'view_count':
            $view_count = get_post_meta($post_id, 'view_count', true);
            echo $view_count ? number_format($view_count) : '0';
            break;
            
        case 'chapter_count':
            $novel_title = get_the_title($post_id);
            $chapter_count = get_posts(array(
                'post_type' => 'post',
                'posts_per_page' => -1,
                'meta_query' => array(
                    array(
                        'key' => 'novel_title',
                        'value' => $novel_title,
                        'compare' => '='
                    )
                ),
                'fields' => 'ids'
            ));
            echo count($chapter_count);
            break;
    }
}
add_action('manage_novel_posts_custom_column', 'epic_novel_admin_column_content', 10, 2);

/**
 * Make custom columns sortable
 */
function epic_novel_sortable_columns($columns) {
    $columns['novel_status'] = 'novel_status';
    $columns['novel_rating'] = 'rating';
    $columns['view_count'] = 'view_count';
    return $columns;
}
add_filter('manage_edit-novel_sortable_columns', 'epic_novel_sortable_columns');

/**
 * Handle sorting for custom columns
 */
function epic_novel_column_orderby($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }
    
    $orderby = $query->get('orderby');
    
    if ($orderby === 'novel_status') {
        $query->set('meta_key', 'novel_status');
        $query->set('orderby', 'meta_value');
    } elseif ($orderby === 'rating') {
        $query->set('meta_key', 'rating');
        $query->set('orderby', 'meta_value_num');
    } elseif ($orderby === 'view_count') {
        $query->set('meta_key', 'view_count');
        $query->set('orderby', 'meta_value_num');
    }
}
add_action('pre_get_posts', 'epic_novel_column_orderby');

/**
 * Add admin dashboard widgets
 */
function epic_add_admin_dashboard_widgets() {
    wp_add_dashboard_widget(
        'epic_novel_stats',
        __('Novel Statistics', 'epic-novel-theme'),
        'epic_novel_stats_widget'
    );
    
    wp_add_dashboard_widget(
        'epic_recent_activity',
        __('Recent Novel Activity', 'epic-novel-theme'),
        'epic_recent_activity_widget'
    );
}
add_action('wp_dashboard_setup', 'epic_add_admin_dashboard_widgets');

/**
 * Novel statistics dashboard widget
 */
function epic_novel_stats_widget() {
    global $wpdb;
    
    // Get novel counts
    $novel_counts = wp_count_posts('novel');
    $total_novels = $novel_counts->publish;
    
    // Get chapter counts
    $total_chapters = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'post' AND post_status = 'publish'");
    
    // Get user counts
    $total_users = count_users();
    $registered_users = $total_users['total_users'];
    
    // Get bookmark counts
    $bookmarks_table = $wpdb->prefix . 'epic_bookmarks';
    $total_bookmarks = $wpdb->get_var("SELECT COUNT(*) FROM $bookmarks_table");
    
    // Get reading history counts
    $history_table = $wpdb->prefix . 'epic_reading_history';
    $total_reads = $wpdb->get_var("SELECT COUNT(*) FROM $history_table");
    
    ?>
    <div class="epic-stats-grid">
        <div class="stat-item">
            <div class="stat-number"><?php echo number_format($total_novels); ?></div>
            <div class="stat-label"><?php _e('Total Novels', 'epic-novel-theme'); ?></div>
        </div>
        
        <div class="stat-item">
            <div class="stat-number"><?php echo number_format($total_chapters); ?></div>
            <div class="stat-label"><?php _e('Total Chapters', 'epic-novel-theme'); ?></div>
        </div>
        
        <div class="stat-item">
            <div class="stat-number"><?php echo number_format($registered_users); ?></div>
            <div class="stat-label"><?php _e('Registered Users', 'epic-novel-theme'); ?></div>
        </div>
        
        <div class="stat-item">
            <div class="stat-number"><?php echo number_format($total_bookmarks); ?></div>
            <div class="stat-label"><?php _e('Total Bookmarks', 'epic-novel-theme'); ?></div>
        </div>
        
        <div class="stat-item">
            <div class="stat-number"><?php echo number_format($total_reads); ?></div>
            <div class="stat-label"><?php _e('Chapter Reads', 'epic-novel-theme'); ?></div>
        </div>
    </div>
    
    <style>
    .epic-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }
    
    .stat-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 4px;
        border-left: 4px solid #0073aa;
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #0073aa;
        line-height: 1;
    }
    
    .stat-label {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }
    </style>
    <?php
}

/**
 * Recent activity dashboard widget
 */
function epic_recent_activity_widget() {
    // Get recent novels
    $recent_novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => 5,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    // Get recent chapters
    $recent_chapters = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => 5,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    ?>
    <div class="epic-activity-tabs">
        <div class="tab-nav">
            <button class="tab-button active" data-tab="novels"><?php _e('Recent Novels', 'epic-novel-theme'); ?></button>
            <button class="tab-button" data-tab="chapters"><?php _e('Recent Chapters', 'epic-novel-theme'); ?></button>
        </div>
        
        <div class="tab-content" id="novels-tab">
            <?php if ($recent_novels) : ?>
                <ul class="activity-list">
                    <?php foreach ($recent_novels as $novel) : ?>
                        <li>
                            <a href="<?php echo get_edit_post_link($novel->ID); ?>">
                                <?php echo esc_html($novel->post_title); ?>
                            </a>
                            <span class="activity-date"><?php echo human_time_diff(strtotime($novel->post_date), current_time('timestamp')) . ' ago'; ?></span>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php else : ?>
                <p><?php _e('No novels found.', 'epic-novel-theme'); ?></p>
            <?php endif; ?>
        </div>
        
        <div class="tab-content" id="chapters-tab" style="display: none;">
            <?php if ($recent_chapters) : ?>
                <ul class="activity-list">
                    <?php foreach ($recent_chapters as $chapter) : ?>
                        <li>
                            <a href="<?php echo get_edit_post_link($chapter->ID); ?>">
                                <?php 
                                $novel_title = get_post_meta($chapter->ID, 'novel_title', true);
                                $chapter_number = get_post_meta($chapter->ID, 'chapter_number', true);
                                
                                if ($novel_title && $chapter_number) {
                                    echo esc_html($novel_title . ' - Chapter ' . $chapter_number);
                                } else {
                                    echo esc_html($chapter->post_title);
                                }
                                ?>
                            </a>
                            <span class="activity-date"><?php echo human_time_diff(strtotime($chapter->post_date), current_time('timestamp')) . ' ago'; ?></span>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php else : ?>
                <p><?php _e('No chapters found.', 'epic-novel-theme'); ?></p>
            <?php endif; ?>
        </div>
    </div>
    
    <style>
    .epic-activity-tabs .tab-nav {
        border-bottom: 1px solid #ddd;
        margin-bottom: 15px;
    }
    
    .epic-activity-tabs .tab-button {
        background: none;
        border: none;
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        margin-right: 10px;
    }
    
    .epic-activity-tabs .tab-button.active {
        border-bottom-color: #0073aa;
        color: #0073aa;
        font-weight: bold;
    }
    
    .activity-list {
        margin: 0;
        padding: 0;
        list-style: none;
    }
    
    .activity-list li {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .activity-list li:last-child {
        border-bottom: none;
    }
    
    .activity-date {
        font-size: 11px;
        color: #666;
    }
    </style>
    
    <script>
    jQuery(document).ready(function($) {
        $('.tab-button').on('click', function() {
            const tab = $(this).data('tab');
            
            $('.tab-button').removeClass('active');
            $(this).addClass('active');
            
            $('.tab-content').hide();
            $('#' + tab + '-tab').show();
        });
    });
    </script>
    <?php
}

/**
 * Add admin styles
 */
function epic_admin_styles() {
    ?>
    <style>
    .column-novel_cover {
        width: 60px;
    }
    
    .column-novel_status {
        width: 100px;
    }
    
    .column-novel_rating {
        width: 80px;
    }
    
    .column-view_count {
        width: 80px;
    }
    
    .column-chapter_count {
        width: 80px;
    }
    
    .novel-admin-meta-box .form-table th {
        width: 150px;
    }
    
    .character-count {
        font-size: 11px;
        color: #666;
        margin-top: 5px;
    }
    </style>
    <?php
}
add_action('admin_head', 'epic_admin_styles');

/**
 * Add quick edit support for novel status
 */
function epic_quick_edit_novel_status($column_name, $post_type) {
    if ($column_name !== 'novel_status' || $post_type !== 'novel') {
        return;
    }
    ?>
    <fieldset class="inline-edit-col-right">
        <div class="inline-edit-col">
            <label>
                <span class="title"><?php _e('Status', 'epic-novel-theme'); ?></span>
                <select name="novel_status">
                    <option value=""><?php _e('— No Change —', 'epic-novel-theme'); ?></option>
                    <option value="ongoing"><?php _e('Ongoing', 'epic-novel-theme'); ?></option>
                    <option value="completed"><?php _e('Completed', 'epic-novel-theme'); ?></option>
                    <option value="hiatus"><?php _e('Hiatus', 'epic-novel-theme'); ?></option>
                    <option value="dropped"><?php _e('Dropped', 'epic-novel-theme'); ?></option>
                </select>
            </label>
        </div>
    </fieldset>
    <?php
}
add_action('quick_edit_custom_box', 'epic_quick_edit_novel_status', 10, 2);

/**
 * Save quick edit data
 */
function epic_save_quick_edit_data($post_id) {
    if (isset($_POST['novel_status']) && !empty($_POST['novel_status'])) {
        update_post_meta($post_id, 'novel_status', sanitize_text_field($_POST['novel_status']));
    }
}
add_action('save_post', 'epic_save_quick_edit_data');
