<?php
/**
 * Google OAuth Integration
 * 
 * @package EpicNovelTheme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add Google OAuth settings to customizer
 */
function epic_google_oauth_customizer($wp_customize) {
    // Add Google OAuth section
    $wp_customize->add_section('epic_google_oauth', array(
        'title' => __('Google OAuth Settings', 'epic-novel-theme'),
        'priority' => 30,
        'description' => __('Configure Google OAuth for user authentication.', 'epic-novel-theme'),
    ));
    
    // Google Client ID
    $wp_customize->add_setting('epic_google_client_id', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('epic_google_client_id', array(
        'label' => __('Google Client ID', 'epic-novel-theme'),
        'section' => 'epic_google_oauth',
        'type' => 'text',
        'description' => __('Enter your Google OAuth Client ID from Google Cloud Console.', 'epic-novel-theme'),
    ));
    
    // Google Client Secret
    $wp_customize->add_setting('epic_google_client_secret', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('epic_google_client_secret', array(
        'label' => __('Google Client Secret', 'epic-novel-theme'),
        'section' => 'epic_google_oauth',
        'type' => 'password',
        'description' => __('Enter your Google OAuth Client Secret from Google Cloud Console.', 'epic-novel-theme'),
    ));
    
    // Enable Google Login
    $wp_customize->add_setting('epic_enable_google_login', array(
        'default' => false,
        'sanitize_callback' => 'wp_validate_boolean',
    ));
    
    $wp_customize->add_control('epic_enable_google_login', array(
        'label' => __('Enable Google Login', 'epic-novel-theme'),
        'section' => 'epic_google_oauth',
        'type' => 'checkbox',
        'description' => __('Allow users to login with their Google account.', 'epic-novel-theme'),
    ));
}
add_action('customize_register', 'epic_google_oauth_customizer');

/**
 * AJAX handler for Google OAuth login
 */
function epic_ajax_google_login() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_google_login')) {
        wp_send_json_error('Security check failed');
        return;
    }
    
    // Check if Google login is enabled
    if (!get_theme_mod('epic_enable_google_login', false)) {
        wp_send_json_error('Google login is disabled');
        return;
    }
    
    $credential = sanitize_text_field($_POST['credential']);
    
    if (empty($credential)) {
        wp_send_json_error('No credential provided');
        return;
    }
    
    // Verify the Google JWT token
    $user_data = epic_verify_google_token($credential);
    
    if (!$user_data) {
        wp_send_json_error('Invalid Google token');
        return;
    }
    
    // Check if user exists
    $user = get_user_by('email', $user_data['email']);
    
    if (!$user) {
        // Create new user
        $user_id = epic_create_user_from_google($user_data);
        if (!$user_id) {
            wp_send_json_error('Failed to create user account');
            return;
        }
        $user = get_user_by('id', $user_id);
    } else {
        // Update existing user's Google data
        epic_update_user_google_data($user->ID, $user_data);
    }
    
    // Log the user in
    wp_set_current_user($user->ID);
    wp_set_auth_cookie($user->ID, true);
    
    wp_send_json_success(array(
        'message' => __('Login successful', 'epic-novel-theme'),
        'user_id' => $user->ID,
        'redirect_url' => home_url()
    ));
}
add_action('wp_ajax_nopriv_epic_google_login', 'epic_ajax_google_login');
add_action('wp_ajax_epic_google_login', 'epic_ajax_google_login');

/**
 * Verify Google JWT token
 */
function epic_verify_google_token($credential) {
    $client_id = get_theme_mod('epic_google_client_id');
    
    if (empty($client_id)) {
        return false;
    }
    
    // Decode JWT token (simplified version - in production, use a proper JWT library)
    $parts = explode('.', $credential);
    if (count($parts) !== 3) {
        return false;
    }
    
    $header = json_decode(base64_decode($parts[0]), true);
    $payload = json_decode(base64_decode($parts[1]), true);
    
    // Verify the token with Google's API
    $response = wp_remote_get("https://oauth2.googleapis.com/tokeninfo?id_token=" . $credential);
    
    if (is_wp_error($response)) {
        return false;
    }
    
    $body = wp_remote_retrieve_body($response);
    $token_info = json_decode($body, true);
    
    // Verify the audience (client ID)
    if (!isset($token_info['aud']) || $token_info['aud'] !== $client_id) {
        return false;
    }
    
    // Verify the issuer
    if (!isset($token_info['iss']) || !in_array($token_info['iss'], ['accounts.google.com', 'https://accounts.google.com'])) {
        return false;
    }
    
    // Check expiration
    if (!isset($token_info['exp']) || $token_info['exp'] < time()) {
        return false;
    }
    
    return array(
        'google_id' => $token_info['sub'],
        'email' => $token_info['email'],
        'name' => $token_info['name'],
        'first_name' => $token_info['given_name'] ?? '',
        'last_name' => $token_info['family_name'] ?? '',
        'picture' => $token_info['picture'] ?? '',
        'email_verified' => $token_info['email_verified'] ?? false
    );
}

/**
 * Create new user from Google data
 */
function epic_create_user_from_google($user_data) {
    // Generate username from email
    $username = sanitize_user(current(explode('@', $user_data['email'])));
    
    // Make sure username is unique
    $original_username = $username;
    $counter = 1;
    while (username_exists($username)) {
        $username = $original_username . $counter;
        $counter++;
    }
    
    // Create user
    $user_id = wp_create_user(
        $username,
        wp_generate_password(20, false), // Random password
        $user_data['email']
    );
    
    if (is_wp_error($user_id)) {
        return false;
    }
    
    // Update user meta
    wp_update_user(array(
        'ID' => $user_id,
        'display_name' => $user_data['name'],
        'first_name' => $user_data['first_name'],
        'last_name' => $user_data['last_name']
    ));
    
    // Store Google-specific data
    epic_update_user_google_data($user_id, $user_data);
    
    // Set user role
    $user = new WP_User($user_id);
    $user->set_role('subscriber');
    
    return $user_id;
}

/**
 * Update user's Google data
 */
function epic_update_user_google_data($user_id, $user_data) {
    update_user_meta($user_id, 'google_id', $user_data['google_id']);
    update_user_meta($user_id, 'google_picture', $user_data['picture']);
    update_user_meta($user_id, 'google_email_verified', $user_data['email_verified']);
    update_user_meta($user_id, 'login_method', 'google');
    update_user_meta($user_id, 'last_google_login', current_time('mysql'));
}

/**
 * Add Google login button to login form
 */
function epic_add_google_login_button() {
    if (!get_theme_mod('epic_enable_google_login', false)) {
        return;
    }
    
    $client_id = get_theme_mod('epic_google_client_id');
    if (empty($client_id)) {
        return;
    }
    
    ?>
    <div class="google-login-container">
        <div class="login-separator">
            <span><?php _e('or', 'epic-novel-theme'); ?></span>
        </div>
        
        <div id="g_id_signin" 
             data-type="standard"
             data-size="large"
             data-theme="outline"
             data-text="sign_in_with"
             data-shape="rectangular"
             data-logo_alignment="left">
        </div>
    </div>
    
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <div id="g_id_onload"
         data-client_id="<?php echo esc_attr($client_id); ?>"
         data-callback="handleCredentialResponse"
         data-auto_prompt="false">
    </div>
    
    <script>
    function handleCredentialResponse(response) {
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=epic_google_login&credential=' + response.credential + '&nonce=<?php echo wp_create_nonce('epic_google_login'); ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = data.data.redirect_url || '<?php echo home_url(); ?>';
            } else {
                alert(data.data || 'Login failed. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Login failed. Please try again.');
        });
    }
    </script>
    <?php
}
add_action('login_form', 'epic_add_google_login_button');

/**
 * Custom user roles for the theme
 */
function epic_add_custom_user_roles() {
    // Add Novel Author role
    add_role('novel_author', __('Novel Author', 'epic-novel-theme'), array(
        'read' => true,
        'edit_posts' => true,
        'delete_posts' => true,
        'publish_posts' => true,
        'upload_files' => true,
        'edit_novels' => true,
        'publish_novels' => true,
        'delete_novels' => true,
    ));
    
    // Add Translator role
    add_role('translator', __('Translator', 'epic-novel-theme'), array(
        'read' => true,
        'edit_posts' => true,
        'delete_posts' => true,
        'publish_posts' => true,
        'upload_files' => true,
    ));
    
    // Add capabilities to administrator
    $admin_role = get_role('administrator');
    if ($admin_role) {
        $admin_role->add_cap('edit_novels');
        $admin_role->add_cap('publish_novels');
        $admin_role->add_cap('delete_novels');
        $admin_role->add_cap('edit_others_novels');
        $admin_role->add_cap('delete_others_novels');
    }
    
    // Add capabilities to editor
    $editor_role = get_role('editor');
    if ($editor_role) {
        $editor_role->add_cap('edit_novels');
        $editor_role->add_cap('publish_novels');
        $editor_role->add_cap('delete_novels');
        $editor_role->add_cap('edit_others_novels');
        $editor_role->add_cap('delete_others_novels');
    }
}
add_action('after_switch_theme', 'epic_add_custom_user_roles');

/**
 * Modify user profile to show Google login info
 */
function epic_add_google_profile_fields($user) {
    $google_id = get_user_meta($user->ID, 'google_id', true);
    $google_picture = get_user_meta($user->ID, 'google_picture', true);
    $login_method = get_user_meta($user->ID, 'login_method', true);
    $last_google_login = get_user_meta($user->ID, 'last_google_login', true);
    
    if ($google_id) {
        ?>
        <h3><?php _e('Google Account Information', 'epic-novel-theme'); ?></h3>
        <table class="form-table">
            <tr>
                <th><label><?php _e('Google ID', 'epic-novel-theme'); ?></label></th>
                <td><?php echo esc_html($google_id); ?></td>
            </tr>
            
            <?php if ($google_picture) : ?>
            <tr>
                <th><label><?php _e('Google Profile Picture', 'epic-novel-theme'); ?></label></th>
                <td><img src="<?php echo esc_url($google_picture); ?>" alt="Google Profile Picture" style="width: 50px; height: 50px; border-radius: 50%;"></td>
            </tr>
            <?php endif; ?>
            
            <tr>
                <th><label><?php _e('Login Method', 'epic-novel-theme'); ?></label></th>
                <td><?php echo esc_html(ucfirst($login_method)); ?></td>
            </tr>
            
            <?php if ($last_google_login) : ?>
            <tr>
                <th><label><?php _e('Last Google Login', 'epic-novel-theme'); ?></label></th>
                <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($last_google_login))); ?></td>
            </tr>
            <?php endif; ?>
        </table>
        <?php
    }
}
add_action('show_user_profile', 'epic_add_google_profile_fields');
add_action('edit_user_profile', 'epic_add_google_profile_fields');

/**
 * Use Google profile picture as avatar
 */
function epic_google_avatar($avatar, $id_or_email, $size, $default, $alt) {
    $user = false;
    
    if (is_numeric($id_or_email)) {
        $user = get_user_by('id', $id_or_email);
    } elseif (is_object($id_or_email)) {
        if (!empty($id_or_email->user_id)) {
            $user = get_user_by('id', $id_or_email->user_id);
        }
    } else {
        $user = get_user_by('email', $id_or_email);
    }
    
    if ($user && is_object($user)) {
        $google_picture = get_user_meta($user->ID, 'google_picture', true);
        if ($google_picture) {
            $avatar = "<img alt='{$alt}' src='{$google_picture}' class='avatar avatar-{$size} photo' height='{$size}' width='{$size}' />";
        }
    }
    
    return $avatar;
}
add_filter('get_avatar', 'epic_google_avatar', 10, 5);

/**
 * Admin settings page for Google OAuth
 */
function epic_google_oauth_admin_page() {
    add_options_page(
        __('Google OAuth Settings', 'epic-novel-theme'),
        __('Google OAuth', 'epic-novel-theme'),
        'manage_options',
        'epic-google-oauth',
        'epic_google_oauth_admin_page_content'
    );
}
add_action('admin_menu', 'epic_google_oauth_admin_page');

/**
 * Admin page content for Google OAuth settings
 */
function epic_google_oauth_admin_page_content() {
    if (isset($_POST['submit'])) {
        update_option('epic_google_client_id', sanitize_text_field($_POST['epic_google_client_id']));
        update_option('epic_google_client_secret', sanitize_text_field($_POST['epic_google_client_secret']));
        update_option('epic_enable_google_login', isset($_POST['epic_enable_google_login']));
        
        echo '<div class="notice notice-success"><p>' . __('Settings saved.', 'epic-novel-theme') . '</p></div>';
    }
    
    $client_id = get_option('epic_google_client_id', '');
    $client_secret = get_option('epic_google_client_secret', '');
    $enable_google_login = get_option('epic_enable_google_login', false);
    ?>
    
    <div class="wrap">
        <h1><?php _e('Google OAuth Settings', 'epic-novel-theme'); ?></h1>
        
        <form method="post" action="">
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="epic_google_client_id"><?php _e('Google Client ID', 'epic-novel-theme'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="epic_google_client_id" name="epic_google_client_id" value="<?php echo esc_attr($client_id); ?>" class="regular-text" />
                        <p class="description"><?php _e('Enter your Google OAuth Client ID from Google Cloud Console.', 'epic-novel-theme'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="epic_google_client_secret"><?php _e('Google Client Secret', 'epic-novel-theme'); ?></label>
                    </th>
                    <td>
                        <input type="password" id="epic_google_client_secret" name="epic_google_client_secret" value="<?php echo esc_attr($client_secret); ?>" class="regular-text" />
                        <p class="description"><?php _e('Enter your Google OAuth Client Secret from Google Cloud Console.', 'epic-novel-theme'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Enable Google Login', 'epic-novel-theme'); ?></th>
                    <td>
                        <label for="epic_enable_google_login">
                            <input type="checkbox" id="epic_enable_google_login" name="epic_enable_google_login" value="1" <?php checked($enable_google_login); ?> />
                            <?php _e('Allow users to login with their Google account', 'epic-novel-theme'); ?>
                        </label>
                    </td>
                </tr>
            </table>
            
            <?php submit_button(); ?>
        </form>
        
        <div class="card">
            <h2><?php _e('Setup Instructions', 'epic-novel-theme'); ?></h2>
            <ol>
                <li><?php _e('Go to the Google Cloud Console', 'epic-novel-theme'); ?></li>
                <li><?php _e('Create a new project or select an existing one', 'epic-novel-theme'); ?></li>
                <li><?php _e('Enable the Google+ API', 'epic-novel-theme'); ?></li>
                <li><?php _e('Create OAuth 2.0 credentials', 'epic-novel-theme'); ?></li>
                <li><?php printf(__('Add %s to authorized redirect URIs', 'epic-novel-theme'), '<code>' . home_url() . '</code>'); ?></li>
                <li><?php _e('Copy the Client ID and Client Secret to the fields above', 'epic-novel-theme'); ?></li>
                <li><?php _e('Enable Google Login and save the settings', 'epic-novel-theme'); ?></li>
            </ol>
        </div>
    </div>
    
    <?php
}
