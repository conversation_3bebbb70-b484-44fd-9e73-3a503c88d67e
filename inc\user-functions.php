<?php
/**
 * User Functions and Database Tables
 * 
 * @package EpicNovelTheme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Create custom database tables
 */
function epic_create_database_tables() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // User bookmarks table
    $bookmarks_table = $wpdb->prefix . 'epic_bookmarks';
    $bookmarks_sql = "CREATE TABLE $bookmarks_table (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        post_id bigint(20) NOT NULL,
        post_type varchar(20) NOT NULL DEFAULT 'post',
        bookmark_date datetime DEFAULT CURRENT_TIMESTAMP,
        notes text,
        PRIMARY KEY (id),
        UNIQUE KEY user_post (user_id, post_id),
        KEY user_id (user_id),
        KEY post_id (post_id)
    ) $charset_collate;";
    
    // Reading history table
    $history_table = $wpdb->prefix . 'epic_reading_history';
    $history_sql = "CREATE TABLE $history_table (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        post_id bigint(20) NOT NULL,
        novel_id bigint(20),
        chapter_number varchar(50),
        reading_progress float DEFAULT 0,
        last_read datetime DEFAULT CURRENT_TIMESTAMP,
        reading_time int DEFAULT 0,
        PRIMARY KEY (id),
        UNIQUE KEY user_post (user_id, post_id),
        KEY user_id (user_id),
        KEY novel_id (novel_id),
        KEY last_read (last_read)
    ) $charset_collate;";
    
    // User preferences table
    $preferences_table = $wpdb->prefix . 'epic_user_preferences';
    $preferences_sql = "CREATE TABLE $preferences_table (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        preference_key varchar(100) NOT NULL,
        preference_value longtext,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_preference (user_id, preference_key),
        KEY user_id (user_id)
    ) $charset_collate;";
    
    // Novel statistics table
    $stats_table = $wpdb->prefix . 'epic_novel_stats';
    $stats_sql = "CREATE TABLE $stats_table (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        novel_id bigint(20) NOT NULL,
        view_count bigint(20) DEFAULT 0,
        bookmark_count bigint(20) DEFAULT 0,
        rating_sum decimal(10,2) DEFAULT 0,
        rating_count int DEFAULT 0,
        last_chapter_date datetime,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY novel_id (novel_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($bookmarks_sql);
    dbDelta($history_sql);
    dbDelta($preferences_sql);
    dbDelta($stats_sql);
}

/**
 * Get user's reading history
 */
function epic_get_user_reading_history($user_id, $limit = 20) {
    global $wpdb;
    
    $history_table = $wpdb->prefix . 'epic_reading_history';
    
    $results = $wpdb->get_results($wpdb->prepare(
        "SELECT h.*, p.post_title, p.post_name, n.post_title as novel_title
         FROM $history_table h
         LEFT JOIN {$wpdb->posts} p ON h.post_id = p.ID
         LEFT JOIN {$wpdb->posts} n ON h.novel_id = n.ID
         WHERE h.user_id = %d
         ORDER BY h.last_read DESC
         LIMIT %d",
        $user_id,
        $limit
    ));
    
    return $results;
}

/**
 * Update reading history
 */
function epic_update_reading_history($user_id, $post_id, $progress = 0, $reading_time = 0) {
    global $wpdb;
    
    if (!$user_id || !$post_id) {
        return false;
    }
    
    $history_table = $wpdb->prefix . 'epic_reading_history';
    
    // Get novel ID from chapter
    $novel_title = get_post_meta($post_id, 'novel_title', true);
    $novel_id = null;
    
    if ($novel_title) {
        $novel = get_page_by_title($novel_title, OBJECT, 'novel');
        if ($novel) {
            $novel_id = $novel->ID;
        }
    }
    
    $chapter_number = get_post_meta($post_id, 'chapter_number', true);
    
    // Insert or update reading history
    $result = $wpdb->replace(
        $history_table,
        array(
            'user_id' => $user_id,
            'post_id' => $post_id,
            'novel_id' => $novel_id,
            'chapter_number' => $chapter_number,
            'reading_progress' => $progress,
            'last_read' => current_time('mysql'),
            'reading_time' => $reading_time
        ),
        array('%d', '%d', '%d', '%s', '%f', '%s', '%d')
    );
    
    return $result !== false;
}

/**
 * Get user's bookmarks
 */
function epic_get_user_bookmarks($user_id, $post_type = 'all', $limit = 20) {
    global $wpdb;
    
    $bookmarks_table = $wpdb->prefix . 'epic_bookmarks';
    
    $where_clause = "WHERE b.user_id = %d";
    $params = array($user_id);
    
    if ($post_type !== 'all') {
        $where_clause .= " AND b.post_type = %s";
        $params[] = $post_type;
    }
    
    $params[] = $limit;
    
    $results = $wpdb->get_results($wpdb->prepare(
        "SELECT b.*, p.post_title, p.post_name, p.post_type
         FROM $bookmarks_table b
         LEFT JOIN {$wpdb->posts} p ON b.post_id = p.ID
         $where_clause
         ORDER BY b.bookmark_date DESC
         LIMIT %d",
        ...$params
    ));
    
    return $results;
}

/**
 * Add bookmark
 */
function epic_add_bookmark($user_id, $post_id, $notes = '') {
    global $wpdb;
    
    if (!$user_id || !$post_id) {
        return false;
    }
    
    $bookmarks_table = $wpdb->prefix . 'epic_bookmarks';
    $post_type = get_post_type($post_id);
    
    $result = $wpdb->insert(
        $bookmarks_table,
        array(
            'user_id' => $user_id,
            'post_id' => $post_id,
            'post_type' => $post_type,
            'bookmark_date' => current_time('mysql'),
            'notes' => $notes
        ),
        array('%d', '%d', '%s', '%s', '%s')
    );
    
    return $result !== false;
}

/**
 * Remove bookmark
 */
function epic_remove_bookmark($user_id, $post_id) {
    global $wpdb;
    
    $bookmarks_table = $wpdb->prefix . 'epic_bookmarks';
    
    $result = $wpdb->delete(
        $bookmarks_table,
        array(
            'user_id' => $user_id,
            'post_id' => $post_id
        ),
        array('%d', '%d')
    );
    
    return $result !== false;
}

/**
 * Check if post is bookmarked by user
 */
function epic_is_bookmarked($user_id, $post_id) {
    global $wpdb;
    
    if (!$user_id || !$post_id) {
        return false;
    }
    
    $bookmarks_table = $wpdb->prefix . 'epic_bookmarks';
    
    $count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $bookmarks_table WHERE user_id = %d AND post_id = %d",
        $user_id,
        $post_id
    ));
    
    return $count > 0;
}

/**
 * Get user preference
 */
function epic_get_user_preference($user_id, $key, $default = null) {
    global $wpdb;
    
    $preferences_table = $wpdb->prefix . 'epic_user_preferences';
    
    $value = $wpdb->get_var($wpdb->prepare(
        "SELECT preference_value FROM $preferences_table WHERE user_id = %d AND preference_key = %s",
        $user_id,
        $key
    ));
    
    if ($value !== null) {
        return maybe_unserialize($value);
    }
    
    return $default;
}

/**
 * Set user preference
 */
function epic_set_user_preference($user_id, $key, $value) {
    global $wpdb;
    
    $preferences_table = $wpdb->prefix . 'epic_user_preferences';
    
    $serialized_value = maybe_serialize($value);
    
    $result = $wpdb->replace(
        $preferences_table,
        array(
            'user_id' => $user_id,
            'preference_key' => $key,
            'preference_value' => $serialized_value,
            'updated_at' => current_time('mysql')
        ),
        array('%d', '%s', '%s', '%s')
    );
    
    return $result !== false;
}

/**
 * Get novel statistics
 */
function epic_get_novel_stats($novel_id) {
    global $wpdb;
    
    $stats_table = $wpdb->prefix . 'epic_novel_stats';
    
    $stats = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $stats_table WHERE novel_id = %d",
        $novel_id
    ));
    
    if (!$stats) {
        // Create initial stats record
        $wpdb->insert(
            $stats_table,
            array('novel_id' => $novel_id),
            array('%d')
        );
        
        $stats = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $stats_table WHERE novel_id = %d",
            $novel_id
        ));
    }
    
    return $stats;
}

/**
 * Update novel view count
 */
function epic_update_novel_view_count($novel_id) {
    global $wpdb;
    
    $stats_table = $wpdb->prefix . 'epic_novel_stats';
    
    // Ensure stats record exists
    epic_get_novel_stats($novel_id);
    
    $result = $wpdb->query($wpdb->prepare(
        "UPDATE $stats_table SET view_count = view_count + 1, updated_at = %s WHERE novel_id = %d",
        current_time('mysql'),
        $novel_id
    ));
    
    return $result !== false;
}

/**
 * Get reading time estimation
 */
function epic_get_reading_time($post_id) {
    $content = get_post_field('post_content', $post_id);
    $word_count = str_word_count(strip_tags($content));
    
    // Average reading speed: 200-250 words per minute
    $reading_speed = 225;
    $reading_time = ceil($word_count / $reading_speed);
    
    return max(1, $reading_time); // Minimum 1 minute
}

/**
 * Get continue reading data for user
 */
function epic_get_continue_reading($user_id, $limit = 5) {
    global $wpdb;
    
    $history_table = $wpdb->prefix . 'epic_reading_history';
    
    $results = $wpdb->get_results($wpdb->prepare(
        "SELECT h.*, p.post_title, p.post_name, n.post_title as novel_title, n.ID as novel_id
         FROM $history_table h
         LEFT JOIN {$wpdb->posts} p ON h.post_id = p.ID
         LEFT JOIN {$wpdb->posts} n ON h.novel_id = n.ID
         WHERE h.user_id = %d AND h.reading_progress < 100
         ORDER BY h.last_read DESC
         LIMIT %d",
        $user_id,
        $limit
    ));
    
    return $results;
}

/**
 * Get next chapter for a novel
 */
function epic_get_next_chapter($current_post_id) {
    $novel_title = get_post_meta($current_post_id, 'novel_title', true);
    $current_chapter = get_post_meta($current_post_id, 'chapter_number', true);
    
    if (!$novel_title || !$current_chapter) {
        return null;
    }
    
    // Find next chapter
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => 1,
        'meta_query' => array(
            array(
                'key' => 'novel_title',
                'value' => $novel_title,
                'compare' => '='
            ),
            array(
                'key' => 'chapter_number',
                'value' => floatval($current_chapter),
                'compare' => '>',
                'type' => 'NUMERIC'
            )
        ),
        'meta_key' => 'chapter_number',
        'orderby' => 'meta_value_num',
        'order' => 'ASC'
    );
    
    $next_chapters = get_posts($args);
    
    return !empty($next_chapters) ? $next_chapters[0] : null;
}

/**
 * Get previous chapter for a novel
 */
function epic_get_previous_chapter($current_post_id) {
    $novel_title = get_post_meta($current_post_id, 'novel_title', true);
    $current_chapter = get_post_meta($current_post_id, 'chapter_number', true);

    if (!$novel_title || !$current_chapter) {
        return null;
    }

    // Find previous chapter
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => 1,
        'meta_query' => array(
            array(
                'key' => 'novel_title',
                'value' => $novel_title,
                'compare' => '='
            ),
            array(
                'key' => 'chapter_number',
                'value' => floatval($current_chapter),
                'compare' => '<',
                'type' => 'NUMERIC'
            )
        ),
        'meta_key' => 'chapter_number',
        'orderby' => 'meta_value_num',
        'order' => 'DESC'
    );

    $prev_chapters = get_posts($args);

    return !empty($prev_chapters) ? $prev_chapters[0] : null;
}

/**
 * Get related novels based on genres and tags
 */
function epic_get_related_novels($novel_id, $limit = 5) {
    // Get current novel's genres and tags
    $genres = get_the_terms($novel_id, 'genre');
    $tags = get_the_terms($novel_id, 'novel_tag');

    $tax_query = array('relation' => 'OR');

    if ($genres && !is_wp_error($genres)) {
        $tax_query[] = array(
            'taxonomy' => 'genre',
            'field' => 'term_id',
            'terms' => wp_list_pluck($genres, 'term_id')
        );
    }

    if ($tags && !is_wp_error($tags)) {
        $tax_query[] = array(
            'taxonomy' => 'novel_tag',
            'field' => 'term_id',
            'terms' => wp_list_pluck($tags, 'term_id')
        );
    }

    if (count($tax_query) === 1) {
        return array(); // No genres or tags to match
    }

    $args = array(
        'post_type' => 'novel',
        'posts_per_page' => $limit + 1, // Get one extra to exclude current
        'post_status' => 'publish',
        'post__not_in' => array($novel_id),
        'tax_query' => $tax_query,
        'orderby' => 'rand'
    );

    $related_novels = get_posts($args);

    // Remove current novel if it somehow got included
    $related_novels = array_filter($related_novels, function($novel) use ($novel_id) {
        return $novel->ID !== $novel_id;
    });

    return array_slice($related_novels, 0, $limit);
}
