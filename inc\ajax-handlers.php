<?php
/**
 * AJAX Handlers for Theme Features
 * 
 * @package EpicNovelTheme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AJAX handler for loading more novels
 */
function epic_ajax_load_more_novels() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    $page = intval($_POST['page']);
    $posts_per_page = intval($_POST['posts_per_page']) ?: 12;
    $genre = sanitize_text_field($_POST['genre'] ?? '');
    $status = sanitize_text_field($_POST['status'] ?? '');
    $orderby = sanitize_text_field($_POST['orderby'] ?? 'date');
    $search = sanitize_text_field($_POST['search'] ?? '');
    
    $args = array(
        'post_type' => 'novel',
        'posts_per_page' => $posts_per_page,
        'paged' => $page,
        'post_status' => 'publish'
    );
    
    // Add search query
    if (!empty($search)) {
        $args['s'] = $search;
    }
    
    // Add genre filter
    if (!empty($genre)) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'genre',
                'field' => 'slug',
                'terms' => $genre
            )
        );
    }
    
    // Add status filter
    if (!empty($status)) {
        $args['meta_query'] = array(
            array(
                'key' => 'novel_status',
                'value' => $status,
                'compare' => '='
            )
        );
    }
    
    // Add ordering
    switch ($orderby) {
        case 'title':
            $args['orderby'] = 'title';
            $args['order'] = 'ASC';
            break;
        case 'popularity':
            $args['meta_key'] = 'view_count';
            $args['orderby'] = 'meta_value_num';
            $args['order'] = 'DESC';
            break;
        case 'rating':
            $args['meta_key'] = 'rating';
            $args['orderby'] = 'meta_value_num';
            $args['order'] = 'DESC';
            break;
        default:
            $args['orderby'] = 'date';
            $args['order'] = 'DESC';
    }
    
    $query = new WP_Query($args);
    
    $novels = array();
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            
            $novel_id = get_the_ID();
            $novel_status = get_post_meta($novel_id, 'novel_status', true);
            $rating = get_post_meta($novel_id, 'rating', true);
            $view_count = get_post_meta($novel_id, 'view_count', true);
            
            // Get taxonomies
            $genres = get_the_terms($novel_id, 'genre');
            $writers = get_the_terms($novel_id, 'writer');
            
            $novels[] = array(
                'id' => $novel_id,
                'title' => get_the_title(),
                'url' => get_permalink(),
                'excerpt' => get_the_excerpt(),
                'thumbnail' => get_the_post_thumbnail_url($novel_id, 'novel-thumbnail'),
                'status' => $novel_status,
                'rating' => $rating,
                'view_count' => $view_count,
                'genres' => $genres ? wp_list_pluck($genres, 'name') : array(),
                'writers' => $writers ? wp_list_pluck($writers, 'name') : array(),
                'is_bookmarked' => is_user_logged_in() ? epic_is_bookmarked(get_current_user_id(), $novel_id) : false
            );
        }
        wp_reset_postdata();
    }
    
    wp_send_json_success(array(
        'novels' => $novels,
        'page' => $page,
        'max_pages' => $query->max_num_pages,
        'has_more' => $page < $query->max_num_pages
    ));
}
add_action('wp_ajax_epic_load_more_novels', 'epic_ajax_load_more_novels');
add_action('wp_ajax_nopriv_epic_load_more_novels', 'epic_ajax_load_more_novels');

/**
 * AJAX handler for searching novels
 */
function epic_ajax_search_novels() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    $search_term = sanitize_text_field($_POST['search_term']);
    $limit = intval($_POST['limit']) ?: 10;
    
    if (empty($search_term)) {
        wp_send_json_success(array('novels' => array()));
        return;
    }
    
    $args = array(
        'post_type' => 'novel',
        'posts_per_page' => $limit,
        's' => $search_term,
        'post_status' => 'publish'
    );
    
    $query = new WP_Query($args);
    
    $novels = array();
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            
            $novel_id = get_the_ID();
            $alternative_title = get_post_meta($novel_id, 'alternative_title', true);
            
            $novels[] = array(
                'id' => $novel_id,
                'title' => get_the_title(),
                'alternative_title' => $alternative_title,
                'url' => get_permalink(),
                'thumbnail' => get_the_post_thumbnail_url($novel_id, 'thumbnail')
            );
        }
        wp_reset_postdata();
    }
    
    wp_send_json_success(array('novels' => $novels));
}
add_action('wp_ajax_epic_search_novels', 'epic_ajax_search_novels');
add_action('wp_ajax_nopriv_epic_search_novels', 'epic_ajax_search_novels');

/**
 * AJAX handler for getting novel chapters
 */
function epic_ajax_get_novel_chapters() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    $novel_title = sanitize_text_field($_POST['novel_title']);
    $page = intval($_POST['page']) ?: 1;
    $per_page = intval($_POST['per_page']) ?: 20;
    
    if (empty($novel_title)) {
        wp_send_json_error('Novel title is required');
        return;
    }
    
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => $per_page,
        'paged' => $page,
        'meta_query' => array(
            array(
                'key' => 'novel_title',
                'value' => $novel_title,
                'compare' => '='
            )
        ),
        'meta_key' => 'chapter_number',
        'orderby' => 'meta_value_num',
        'order' => 'ASC'
    );
    
    $query = new WP_Query($args);
    
    $chapters = array();
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            
            $post_id = get_the_ID();
            $chapter_number = get_post_meta($post_id, 'chapter_number', true);
            $volume_title = get_post_meta($post_id, 'volume_title', true);
            $reading_time = epic_get_reading_time($post_id);
            
            $chapters[] = array(
                'id' => $post_id,
                'title' => get_the_title(),
                'url' => get_permalink(),
                'chapter_number' => $chapter_number,
                'volume_title' => $volume_title,
                'date' => get_the_date(),
                'reading_time' => $reading_time,
                'excerpt' => get_the_excerpt(),
                'is_bookmarked' => is_user_logged_in() ? epic_is_bookmarked(get_current_user_id(), $post_id) : false
            );
        }
        wp_reset_postdata();
    }
    
    wp_send_json_success(array(
        'chapters' => $chapters,
        'page' => $page,
        'max_pages' => $query->max_num_pages,
        'has_more' => $page < $query->max_num_pages
    ));
}
add_action('wp_ajax_epic_get_novel_chapters', 'epic_ajax_get_novel_chapters');
add_action('wp_ajax_nopriv_epic_get_novel_chapters', 'epic_ajax_get_novel_chapters');

/**
 * AJAX handler for updating user preferences
 */
function epic_ajax_update_user_preferences() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $user_id = get_current_user_id();
    $preferences = $_POST['preferences'];
    
    if (!is_array($preferences)) {
        wp_send_json_error('Invalid preferences data');
        return;
    }
    
    $allowed_preferences = array(
        'font_size',
        'night_mode',
        'reading_mode',
        'auto_bookmark',
        'email_notifications',
        'chapter_notifications'
    );
    
    $updated = 0;
    foreach ($preferences as $key => $value) {
        if (in_array($key, $allowed_preferences)) {
            $sanitized_value = sanitize_text_field($value);
            if (epic_set_user_preference($user_id, $key, $sanitized_value)) {
                $updated++;
            }
        }
    }
    
    wp_send_json_success(array(
        'message' => sprintf(__('%d preferences updated', 'epic-novel-theme'), $updated),
        'updated_count' => $updated
    ));
}
add_action('wp_ajax_epic_update_user_preferences', 'epic_ajax_update_user_preferences');

/**
 * AJAX handler for getting user dashboard data
 */
function epic_ajax_get_dashboard_data() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $user_id = get_current_user_id();
    
    // Get continue reading
    $continue_reading = epic_get_continue_reading($user_id, 5);
    
    // Get recent bookmarks
    $recent_bookmarks = epic_get_user_bookmarks($user_id, 'all', 5);
    
    // Get reading statistics
    global $wpdb;
    $history_table = $wpdb->prefix . 'epic_reading_history';
    $bookmarks_table = $wpdb->prefix . 'epic_bookmarks';
    
    $total_chapters_read = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $history_table WHERE user_id = %d",
        $user_id
    ));
    
    $total_bookmarks = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $bookmarks_table WHERE user_id = %d",
        $user_id
    ));
    
    $total_reading_time = $wpdb->get_var($wpdb->prepare(
        "SELECT SUM(reading_time) FROM $history_table WHERE user_id = %d",
        $user_id
    ));
    
    // Format continue reading data
    $formatted_continue_reading = array();
    foreach ($continue_reading as $item) {
        $formatted_continue_reading[] = array(
            'post_id' => $item->post_id,
            'post_title' => $item->post_title,
            'post_url' => get_permalink($item->post_id),
            'novel_title' => $item->novel_title,
            'chapter_number' => $item->chapter_number,
            'reading_progress' => $item->reading_progress,
            'last_read' => $item->last_read
        );
    }
    
    // Format recent bookmarks
    $formatted_bookmarks = array();
    foreach ($recent_bookmarks as $bookmark) {
        $post = get_post($bookmark->post_id);
        if ($post) {
            $formatted_bookmarks[] = array(
                'post_id' => $bookmark->post_id,
                'post_title' => $post->post_title,
                'post_url' => get_permalink($post->ID),
                'post_type' => $bookmark->post_type,
                'bookmark_date' => $bookmark->bookmark_date
            );
        }
    }
    
    wp_send_json_success(array(
        'continue_reading' => $formatted_continue_reading,
        'recent_bookmarks' => $formatted_bookmarks,
        'statistics' => array(
            'total_chapters_read' => intval($total_chapters_read),
            'total_bookmarks' => intval($total_bookmarks),
            'total_reading_time' => intval($total_reading_time),
            'reading_time_hours' => round(intval($total_reading_time) / 3600, 1)
        )
    ));
}
add_action('wp_ajax_epic_get_dashboard_data', 'epic_ajax_get_dashboard_data');

/**
 * AJAX handler for rating novels
 */
function epic_ajax_rate_novel() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('User not logged in');
        return;
    }
    
    $user_id = get_current_user_id();
    $novel_id = intval($_POST['novel_id']);
    $rating = floatval($_POST['rating']);
    
    if (!$novel_id || $rating < 1 || $rating > 10) {
        wp_send_json_error('Invalid rating data');
        return;
    }
    
    // Check if user has already rated this novel
    $existing_rating = get_user_meta($user_id, 'novel_rating_' . $novel_id, true);
    
    if ($existing_rating) {
        wp_send_json_error('You have already rated this novel');
        return;
    }
    
    // Save user's rating
    update_user_meta($user_id, 'novel_rating_' . $novel_id, $rating);
    
    // Update novel's average rating
    $ratings = get_post_meta($novel_id, 'user_ratings', true) ?: array();
    $ratings[$user_id] = $rating;
    update_post_meta($novel_id, 'user_ratings', $ratings);
    
    $average_rating = array_sum($ratings) / count($ratings);
    update_post_meta($novel_id, 'rating', round($average_rating, 1));
    update_post_meta($novel_id, 'rating_count', count($ratings));
    
    wp_send_json_success(array(
        'message' => __('Rating submitted successfully', 'epic-novel-theme'),
        'average_rating' => round($average_rating, 1),
        'rating_count' => count($ratings)
    ));
}
add_action('wp_ajax_epic_rate_novel', 'epic_ajax_rate_novel');

/**
 * AJAX handler for getting novel statistics
 */
function epic_ajax_get_novel_stats() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }
    
    $novel_id = intval($_POST['novel_id']);
    
    if (!$novel_id) {
        wp_send_json_error('Invalid novel ID');
        return;
    }
    
    $stats = epic_get_novel_stats($novel_id);
    
    // Get chapter count
    $novel_title = get_the_title($novel_id);
    $chapter_count = 0;
    
    if ($novel_title) {
        $chapter_count = get_posts(array(
            'post_type' => 'post',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => 'novel_title',
                    'value' => $novel_title,
                    'compare' => '='
                )
            ),
            'fields' => 'ids'
        ));
        $chapter_count = count($chapter_count);
    }
    
    // Get rating info
    $rating = get_post_meta($novel_id, 'rating', true);
    $rating_count = get_post_meta($novel_id, 'rating_count', true);
    
    wp_send_json_success(array(
        'view_count' => intval($stats->view_count),
        'bookmark_count' => intval($stats->bookmark_count),
        'chapter_count' => $chapter_count,
        'rating' => floatval($rating),
        'rating_count' => intval($rating_count),
        'last_chapter_date' => $stats->last_chapter_date
    ));
}
add_action('wp_ajax_epic_get_novel_stats', 'epic_ajax_get_novel_stats');
add_action('wp_ajax_nopriv_epic_get_novel_stats', 'epic_ajax_get_novel_stats');

/**
 * AJAX handler for loading more novels on homepage
 */
function epic_ajax_load_more_homepage_novels() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'epic_nonce')) {
        wp_die('Security check failed');
    }

    $page = intval($_POST['page']) ?: 1;
    $per_page = 12;

    $novels_with_chapters = epic_get_novels_with_recent_chapters($per_page, $page);

    $novels_data = array();

    foreach ($novels_with_chapters as $novel_data) {
        $novel = $novel_data['novel'];
        $recent_chapters = $novel_data['recent_chapters'];
        $latest_chapter_date = $novel_data['latest_chapter_date'];

        // Get novel metadata
        $alternative_title = get_post_meta($novel->ID, 'alternative_title', true);
        $novel_status = get_post_meta($novel->ID, 'novel_status', true);
        $rating = get_post_meta($novel->ID, 'rating', true);
        $novel_summary = get_post_meta($novel->ID, 'novel_summary', true);

        // Get taxonomies
        $writers = get_the_terms($novel->ID, 'writer');

        // Format recent chapters
        $formatted_chapters = array();
        foreach ($recent_chapters as $chapter) {
            $chapter_number = get_post_meta($chapter->ID, 'chapter_number', true);
            $volume_title = get_post_meta($chapter->ID, 'volume_title', true);

            $formatted_chapters[] = array(
                'id' => $chapter->ID,
                'title' => $chapter->post_title,
                'url' => get_permalink($chapter->ID),
                'chapter_number' => $chapter_number,
                'volume_title' => $volume_title,
                'date' => get_the_date('M j, Y', $chapter->ID),
                'reading_time' => epic_get_reading_time($chapter->ID),
                'is_bookmarked' => is_user_logged_in() ? epic_is_bookmarked(get_current_user_id(), $chapter->ID) : false
            );
        }

        $novels_data[] = array(
            'id' => $novel->ID,
            'title' => $novel->post_title,
            'url' => get_permalink($novel->ID),
            'alternative_title' => $alternative_title,
            'thumbnail' => get_the_post_thumbnail_url($novel->ID, 'novel-thumbnail'),
            'status' => $novel_status,
            'rating' => $rating,
            'summary' => $novel_summary ?: $novel->post_excerpt ?: $novel->post_content,
            'writers' => $writers ? wp_list_pluck($writers, 'name') : array(),
            'recent_chapters' => $formatted_chapters,
            'latest_chapter_date' => $latest_chapter_date,
            'time_ago' => human_time_diff(strtotime($latest_chapter_date), current_time('timestamp')),
            'is_bookmarked' => is_user_logged_in() ? epic_is_bookmarked(get_current_user_id(), $novel->ID) : false
        );
    }

    // Check if there are more novels
    $total_novels = epic_get_novels_with_chapters_count();
    $total_loaded = ($page * 12);
    $has_more = $total_loaded < $total_novels;

    wp_send_json_success(array(
        'novels' => $novels_data,
        'page' => $page,
        'has_more' => $has_more,
        'total' => $total_novels,
        'loaded' => count($novels_data)
    ));
}
add_action('wp_ajax_epic_load_more_homepage_novels', 'epic_ajax_load_more_homepage_novels');
add_action('wp_ajax_nopriv_epic_load_more_homepage_novels', 'epic_ajax_load_more_homepage_novels');
