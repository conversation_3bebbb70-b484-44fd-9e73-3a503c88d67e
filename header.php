<?php
/**
 * The header for our theme
 * 
 * @package EpicNovelTheme
 */
?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <?php wp_head(); ?>
    
    <!-- SEO Meta Tags -->
    <?php epic_render_seo_meta(); ?>
    
    <!-- Schema Markup -->
    <?php epic_render_schema_markup(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<!-- Reading Progress Bar -->
<div class="reading-progress" id="reading-progress"></div>

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#main"><?php _e('Skip to content', 'epic-novel-theme'); ?></a>

    <header id="masthead" class="site-header">
        <div class="container">
            <div class="header-content">
                
                <!-- Site Branding -->
                <div class="site-branding">
                    <?php
                    if (has_custom_logo()) {
                        the_custom_logo();
                    } else {
                        if (is_front_page() && is_home()) :
                            ?>
                            <h1 class="site-title">
                                <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                                    <?php bloginfo('name'); ?>
                                </a>
                            </h1>
                            <?php
                        else :
                            ?>
                            <p class="site-title">
                                <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                                    <?php bloginfo('name'); ?>
                                </a>
                            </p>
                            <?php
                        endif;
                        
                        $description = get_bloginfo('description', 'display');
                        if ($description || is_customize_preview()) :
                            ?>
                            <p class="site-description"><?php echo $description; ?></p>
                            <?php
                        endif;
                    }
                    ?>
                </div>

                <!-- Main Navigation -->
                <nav id="site-navigation" class="main-navigation">
                    <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                        <span class="menu-toggle-text"><?php _e('Menu', 'epic-novel-theme'); ?></span>
                        <span class="menu-icon">
                            <span></span>
                            <span></span>
                            <span></span>
                        </span>
                    </button>
                    
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_id'        => 'primary-menu',
                        'container'      => false,
                        'fallback_cb'    => 'epic_fallback_menu',
                    ));
                    ?>
                </nav>

                <!-- User Controls -->
                <div class="user-controls">
                    
                    <!-- Search Form -->
                    <div class="header-search">
                        <button class="search-toggle" aria-expanded="false">
                            <i class="fas fa-search"></i>
                            <span class="screen-reader-text"><?php _e('Search', 'epic-novel-theme'); ?></span>
                        </button>
                        <div class="search-form-container">
                            <?php get_search_form(); ?>
                        </div>
                    </div>

                    <!-- Night Mode Toggle -->
                    <button class="night-mode-toggle" id="night-mode-toggle" aria-label="<?php _e('Toggle night mode', 'epic-novel-theme'); ?>">
                        <i class="fas fa-moon"></i>
                        <span class="toggle-text"><?php _e('Night Mode', 'epic-novel-theme'); ?></span>
                    </button>

                    <!-- User Authentication -->
                    <div class="user-auth">
                        <?php if (is_user_logged_in()) : ?>
                            <?php $current_user = wp_get_current_user(); ?>
                            <div class="user-menu">
                                <button class="user-menu-toggle" aria-expanded="false">
                                    <?php echo get_avatar($current_user->ID, 32); ?>
                                    <span class="user-name"><?php echo esc_html($current_user->display_name); ?></span>
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <div class="user-dropdown">
                                    <ul>
                                        <li><a href="<?php echo esc_url(home_url('/dashboard')); ?>">
                                            <i class="fas fa-tachometer-alt"></i>
                                            <?php _e('Dashboard', 'epic-novel-theme'); ?>
                                        </a></li>
                                        <li><a href="<?php echo esc_url(home_url('/bookmarks')); ?>">
                                            <i class="fas fa-bookmark"></i>
                                            <?php _e('Bookmarks', 'epic-novel-theme'); ?>
                                        </a></li>
                                        <li><a href="<?php echo esc_url(home_url('/reading-history')); ?>">
                                            <i class="fas fa-history"></i>
                                            <?php _e('Reading History', 'epic-novel-theme'); ?>
                                        </a></li>
                                        <li><a href="<?php echo esc_url(wp_logout_url(home_url())); ?>">
                                            <i class="fas fa-sign-out-alt"></i>
                                            <?php _e('Logout', 'epic-novel-theme'); ?>
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        <?php else : ?>
                            <div class="login-buttons">
                                <a href="<?php echo esc_url(wp_login_url()); ?>" class="login-btn">
                                    <i class="fas fa-sign-in-alt"></i>
                                    <?php _e('Login', 'epic-novel-theme'); ?>
                                </a>
                                <button class="google-login-btn" id="google-login-btn">
                                    <i class="fab fa-google"></i>
                                    <?php _e('Login with Google', 'epic-novel-theme'); ?>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Search Overlay -->
    <div class="mobile-search-overlay" id="mobile-search-overlay">
        <div class="mobile-search-content">
            <button class="close-search" aria-label="<?php _e('Close search', 'epic-novel-theme'); ?>">
                <i class="fas fa-times"></i>
            </button>
            <?php get_search_form(); ?>
        </div>
    </div>

    <!-- Advanced Search and Filters (for novel archive pages) -->
    <?php if (is_post_type_archive('novel') || is_tax(array('genre', 'writer', 'artist', 'novel_type', 'novel_tag'))) : ?>
        <div class="search-filters">
            <div class="container">
                <form class="novel-filters" method="get" action="<?php echo esc_url(get_post_type_archive_link('novel')); ?>">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="search-novels"><?php _e('Search Novels', 'epic-novel-theme'); ?></label>
                            <input type="text" id="search-novels" name="s" value="<?php echo get_search_query(); ?>" placeholder="<?php _e('Enter novel title or keyword...', 'epic-novel-theme'); ?>">
                        </div>
                        
                        <div class="filter-group">
                            <label for="filter-genre"><?php _e('Genre', 'epic-novel-theme'); ?></label>
                            <select id="filter-genre" name="genre">
                                <option value=""><?php _e('All Genres', 'epic-novel-theme'); ?></option>
                                <?php
                                $genres = get_terms(array('taxonomy' => 'genre', 'hide_empty' => true));
                                foreach ($genres as $genre) {
                                    $selected = (isset($_GET['genre']) && $_GET['genre'] == $genre->slug) ? 'selected' : '';
                                    echo '<option value="' . esc_attr($genre->slug) . '" ' . $selected . '>' . esc_html($genre->name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="filter-status"><?php _e('Status', 'epic-novel-theme'); ?></label>
                            <select id="filter-status" name="status">
                                <option value=""><?php _e('All Status', 'epic-novel-theme'); ?></option>
                                <option value="ongoing" <?php selected(isset($_GET['status']) ? $_GET['status'] : '', 'ongoing'); ?>><?php _e('Ongoing', 'epic-novel-theme'); ?></option>
                                <option value="completed" <?php selected(isset($_GET['status']) ? $_GET['status'] : '', 'completed'); ?>><?php _e('Completed', 'epic-novel-theme'); ?></option>
                                <option value="hiatus" <?php selected(isset($_GET['status']) ? $_GET['status'] : '', 'hiatus'); ?>><?php _e('Hiatus', 'epic-novel-theme'); ?></option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="filter-sort"><?php _e('Sort By', 'epic-novel-theme'); ?></label>
                            <select id="filter-sort" name="orderby">
                                <option value="date" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'date'); ?>><?php _e('Latest', 'epic-novel-theme'); ?></option>
                                <option value="title" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'title'); ?>><?php _e('Title A-Z', 'epic-novel-theme'); ?></option>
                                <option value="popularity" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'popularity'); ?>><?php _e('Popularity', 'epic-novel-theme'); ?></option>
                                <option value="rating" <?php selected(isset($_GET['orderby']) ? $_GET['orderby'] : '', 'rating'); ?>><?php _e('Rating', 'epic-novel-theme'); ?></option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="filter-actions">
                        <button type="submit" class="filter-submit">
                            <i class="fas fa-search"></i>
                            <?php _e('Filter Novels', 'epic-novel-theme'); ?>
                        </button>
                        <a href="<?php echo esc_url(get_post_type_archive_link('novel')); ?>" class="filter-reset">
                            <i class="fas fa-undo"></i>
                            <?php _e('Reset Filters', 'epic-novel-theme'); ?>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>
